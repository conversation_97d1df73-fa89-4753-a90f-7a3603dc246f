import Database from 'better-sqlite3';
import * as path from 'path';
import * as fs from 'fs';
import { app } from 'electron';

export interface Project {
  id: number;
  name: string;
  path: string;
  lastOpened: string;
  settings: string; // JSON string
  created: string;
}

export interface UserSettings {
  id: number;
  key: string;
  value: string;
  category: string;
}

export interface Extension {
  id: number;
  name: string;
  version: string;
  type: 'vsix' | 'koix';
  enabled: boolean;
  path: string;
  manifest: string; // JSON string
  installed: string;
}

export interface Theme {
  id: number;
  name: string;
  type: 'builtin' | 'custom';
  path: string;
  manifest: string; // JSON string
  active: boolean;
  installed: string;
}

export interface Session {
  id: number;
  projectId: number;
  openFiles: string; // JSON array of file paths
  activeFile: string | null;
  layout: string; // JSON string for layout state
  created: string;
  updated: string;
}

export class DatabaseManager {
  private db: Database.Database;
  private dbPath: string;

  constructor() {
    // Create database in user data directory
    const userDataPath = app.getPath('userData');
    const dbDir = path.join(userDataPath, 'kilatcode');
    
    // Ensure directory exists
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
    }
    
    this.dbPath = path.join(dbDir, 'kilatcode.db');
    this.db = new Database(this.dbPath);
    
    // Enable WAL mode for better performance
    this.db.pragma('journal_mode = WAL');
    
    this.initializeTables();
  }

  private initializeTables(): void {
    // Projects table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS projects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        path TEXT UNIQUE NOT NULL,
        lastOpened DATETIME DEFAULT CURRENT_TIMESTAMP,
        settings TEXT DEFAULT '{}',
        created DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // User settings table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS user_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT NOT NULL,
        value TEXT NOT NULL,
        category TEXT DEFAULT 'general',
        UNIQUE(key, category)
      )
    `);

    // Extensions table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS extensions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        version TEXT NOT NULL,
        type TEXT CHECK(type IN ('vsix', 'koix')) NOT NULL,
        enabled BOOLEAN DEFAULT 1,
        path TEXT NOT NULL,
        manifest TEXT NOT NULL,
        installed DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(name, version)
      )
    `);

    // Themes table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS themes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL,
        type TEXT CHECK(type IN ('builtin', 'custom')) NOT NULL,
        path TEXT NOT NULL,
        manifest TEXT NOT NULL,
        active BOOLEAN DEFAULT 0,
        installed DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Sessions table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS sessions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        projectId INTEGER NOT NULL,
        openFiles TEXT DEFAULT '[]',
        activeFile TEXT,
        layout TEXT DEFAULT '{}',
        created DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (projectId) REFERENCES projects (id) ON DELETE CASCADE
      )
    `);

    // Create indexes for better performance
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_projects_path ON projects(path);
      CREATE INDEX IF NOT EXISTS idx_projects_lastOpened ON projects(lastOpened);
      CREATE INDEX IF NOT EXISTS idx_user_settings_category ON user_settings(category);
      CREATE INDEX IF NOT EXISTS idx_extensions_enabled ON extensions(enabled);
      CREATE INDEX IF NOT EXISTS idx_themes_active ON themes(active);
      CREATE INDEX IF NOT EXISTS idx_sessions_projectId ON sessions(projectId);
    `);

    // Insert default settings if not exists
    this.insertDefaultSettings();
  }

  private insertDefaultSettings(): void {
    const defaultSettings = [
      { key: 'theme', value: 'nusantara-glow', category: 'appearance' },
      { key: 'fontSize', value: '14', category: 'editor' },
      { key: 'fontFamily', value: 'Fira Code, Consolas, monospace', category: 'editor' },
      { key: 'tabSize', value: '2', category: 'editor' },
      { key: 'wordWrap', value: 'on', category: 'editor' },
      { key: 'minimap', value: 'true', category: 'editor' },
      { key: 'lineNumbers', value: 'on', category: 'editor' },
      { key: 'autoSave', value: 'afterDelay', category: 'files' },
      { key: 'autoSaveDelay', value: '1000', category: 'files' },
      { key: 'terminalShell', value: '', category: 'terminal' },
      { key: 'terminalFontSize', value: '14', category: 'terminal' },
      { key: 'aiProvider', value: 'local', category: 'ai' },
      { key: 'aiModel', value: 'mbah-ai-local', category: 'ai' }
    ];

    const insertSetting = this.db.prepare(`
      INSERT OR IGNORE INTO user_settings (key, value, category) 
      VALUES (?, ?, ?)
    `);

    for (const setting of defaultSettings) {
      insertSetting.run(setting.key, setting.value, setting.category);
    }
  }

  // Project methods
  public addProject(name: string, projectPath: string, settings: object = {}): Project {
    const stmt = this.db.prepare(`
      INSERT INTO projects (name, path, settings) 
      VALUES (?, ?, ?)
    `);
    
    const result = stmt.run(name, projectPath, JSON.stringify(settings));
    
    return this.getProject(result.lastInsertRowid as number)!;
  }

  public getProject(id: number): Project | null {
    const stmt = this.db.prepare('SELECT * FROM projects WHERE id = ?');
    return stmt.get(id) as Project | null;
  }

  public getProjectByPath(projectPath: string): Project | null {
    const stmt = this.db.prepare('SELECT * FROM projects WHERE path = ?');
    return stmt.get(projectPath) as Project | null;
  }

  public getRecentProjects(limit: number = 10): Project[] {
    const stmt = this.db.prepare(`
      SELECT * FROM projects 
      ORDER BY lastOpened DESC 
      LIMIT ?
    `);
    return stmt.all(limit) as Project[];
  }

  public updateProjectLastOpened(id: number): void {
    const stmt = this.db.prepare(`
      UPDATE projects 
      SET lastOpened = CURRENT_TIMESTAMP 
      WHERE id = ?
    `);
    stmt.run(id);
  }

  public updateProjectSettings(id: number, settings: object): void {
    const stmt = this.db.prepare(`
      UPDATE projects 
      SET settings = ? 
      WHERE id = ?
    `);
    stmt.run(JSON.stringify(settings), id);
  }

  public deleteProject(id: number): void {
    const stmt = this.db.prepare('DELETE FROM projects WHERE id = ?');
    stmt.run(id);
  }

  // Settings methods
  public getSetting(key: string, category: string = 'general'): string | null {
    const stmt = this.db.prepare(`
      SELECT value FROM user_settings 
      WHERE key = ? AND category = ?
    `);
    const result = stmt.get(key, category) as { value: string } | undefined;
    return result?.value || null;
  }

  public setSetting(key: string, value: string, category: string = 'general'): void {
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO user_settings (key, value, category) 
      VALUES (?, ?, ?)
    `);
    stmt.run(key, value, category);
  }

  public getSettingsByCategory(category: string): UserSettings[] {
    const stmt = this.db.prepare(`
      SELECT * FROM user_settings 
      WHERE category = ?
    `);
    return stmt.all(category) as UserSettings[];
  }

  public getAllSettings(): UserSettings[] {
    const stmt = this.db.prepare('SELECT * FROM user_settings ORDER BY category, key');
    return stmt.all() as UserSettings[];
  }

  // Extension methods
  public addExtension(name: string, version: string, type: 'vsix' | 'koix', extensionPath: string, manifest: object): Extension {
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO extensions (name, version, type, path, manifest) 
      VALUES (?, ?, ?, ?, ?)
    `);
    
    const result = stmt.run(name, version, type, extensionPath, JSON.stringify(manifest));
    
    return this.getExtension(result.lastInsertRowid as number)!;
  }

  public getExtension(id: number): Extension | null {
    const stmt = this.db.prepare('SELECT * FROM extensions WHERE id = ?');
    return stmt.get(id) as Extension | null;
  }

  public getEnabledExtensions(): Extension[] {
    const stmt = this.db.prepare('SELECT * FROM extensions WHERE enabled = 1');
    return stmt.all() as Extension[];
  }

  public getAllExtensions(): Extension[] {
    const stmt = this.db.prepare('SELECT * FROM extensions ORDER BY name');
    return stmt.all() as Extension[];
  }

  public toggleExtension(id: number, enabled: boolean): void {
    const stmt = this.db.prepare('UPDATE extensions SET enabled = ? WHERE id = ?');
    stmt.run(enabled ? 1 : 0, id);
  }

  public deleteExtension(id: number): void {
    const stmt = this.db.prepare('DELETE FROM extensions WHERE id = ?');
    stmt.run(id);
  }

  // Theme methods
  public addTheme(name: string, type: 'builtin' | 'custom', themePath: string, manifest: object): Theme {
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO themes (name, type, path, manifest) 
      VALUES (?, ?, ?, ?)
    `);
    
    const result = stmt.run(name, type, themePath, JSON.stringify(manifest));
    
    return this.getTheme(result.lastInsertRowid as number)!;
  }

  public getTheme(id: number): Theme | null {
    const stmt = this.db.prepare('SELECT * FROM themes WHERE id = ?');
    return stmt.get(id) as Theme | null;
  }

  public getActiveTheme(): Theme | null {
    const stmt = this.db.prepare('SELECT * FROM themes WHERE active = 1 LIMIT 1');
    return stmt.get() as Theme | null;
  }

  public getAllThemes(): Theme[] {
    const stmt = this.db.prepare('SELECT * FROM themes ORDER BY name');
    return stmt.all() as Theme[];
  }

  public setActiveTheme(id: number): void {
    // Deactivate all themes first
    this.db.prepare('UPDATE themes SET active = 0').run();
    
    // Activate the selected theme
    const stmt = this.db.prepare('UPDATE themes SET active = 1 WHERE id = ?');
    stmt.run(id);
  }

  public deleteTheme(id: number): void {
    const stmt = this.db.prepare('DELETE FROM themes WHERE id = ?');
    stmt.run(id);
  }

  // Session methods
  public saveSession(projectId: number, openFiles: string[], activeFile: string | null, layout: object): Session {
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO sessions (projectId, openFiles, activeFile, layout, updated) 
      VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
    `);
    
    const result = stmt.run(projectId, JSON.stringify(openFiles), activeFile, JSON.stringify(layout));
    
    return this.getSession(result.lastInsertRowid as number)!;
  }

  public getSession(id: number): Session | null {
    const stmt = this.db.prepare('SELECT * FROM sessions WHERE id = ?');
    return stmt.get(id) as Session | null;
  }

  public getSessionByProject(projectId: number): Session | null {
    const stmt = this.db.prepare(`
      SELECT * FROM sessions 
      WHERE projectId = ? 
      ORDER BY updated DESC 
      LIMIT 1
    `);
    return stmt.get(projectId) as Session | null;
  }

  public deleteSession(id: number): void {
    const stmt = this.db.prepare('DELETE FROM sessions WHERE id = ?');
    stmt.run(id);
  }

  public close(): void {
    this.db.close();
  }
}
