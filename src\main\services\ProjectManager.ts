import * as path from 'path';
import { EventEmitter } from 'events';
import { DatabaseManager, Project } from './DatabaseManager';
import { FileSystemService, FileInfo } from './FileSystemService';

export interface ProjectConfig {
  name: string;
  version: string;
  description?: string;
  main?: string;
  scripts?: Record<string, string>;
  dependencies?: Record<string, string>;
  devDependencies?: Record<string, string>;
  kilatcode?: {
    extensions?: string[];
    themes?: string[];
    settings?: Record<string, any>;
  };
}

export interface ProjectTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  files: Array<{
    path: string;
    content: string;
    template?: boolean;
  }>;
  dependencies?: Record<string, string>;
  devDependencies?: Record<string, string>;
  scripts?: Record<string, string>;
}

export class ProjectManager extends EventEmitter {
  private db: DatabaseManager;
  private fs: FileSystemService;
  private currentProject: Project | null = null;
  private projectWatcher: string | null = null;

  constructor(db: DatabaseManager, fs: FileSystemService) {
    super();
    this.db = db;
    this.fs = fs;
  }

  // Project lifecycle
  public async openProject(projectPath: string): Promise<Project> {
    try {
      // Validate project path
      if (!await this.fs.exists(projectPath)) {
        throw new Error(`Project path does not exist: ${projectPath}`);
      }

      const stats = await this.fs.getFileInfo(projectPath);
      if (!stats.isDirectory) {
        throw new Error(`Project path is not a directory: ${projectPath}`);
      }

      // Check if project already exists in database
      let project = this.db.getProjectByPath(projectPath);
      
      if (!project) {
        // Create new project entry
        const projectName = path.basename(projectPath);
        project = this.db.addProject(projectName, projectPath);
      } else {
        // Update last opened time
        this.db.updateProjectLastOpened(project.id);
      }

      // Close current project if any
      if (this.currentProject) {
        await this.closeProject();
      }

      // Set as current project
      this.currentProject = project;

      // Start watching project directory
      this.startWatching(projectPath);

      // Load project configuration
      await this.loadProjectConfig(projectPath);

      this.emit('projectOpened', project);
      return project;
    } catch (error) {
      throw new Error(`Failed to open project: ${error.message}`);
    }
  }

  public async closeProject(): Promise<void> {
    if (!this.currentProject) return;

    try {
      // Stop watching
      this.stopWatching();

      // Save current session
      // This will be implemented when we add session management

      const project = this.currentProject;
      this.currentProject = null;

      this.emit('projectClosed', project);
    } catch (error) {
      throw new Error(`Failed to close project: ${error.message}`);
    }
  }

  public getCurrentProject(): Project | null {
    return this.currentProject;
  }

  public getRecentProjects(limit: number = 10): Project[] {
    return this.db.getRecentProjects(limit);
  }

  // Project creation
  public async createProject(projectPath: string, template?: ProjectTemplate): Promise<Project> {
    try {
      // Check if directory already exists
      if (await this.fs.exists(projectPath)) {
        throw new Error(`Directory already exists: ${projectPath}`);
      }

      // Create project directory
      await this.fs.ensureDirectory(projectPath);

      // Apply template if provided
      if (template) {
        await this.applyTemplate(projectPath, template);
      } else {
        // Create basic project structure
        await this.createBasicProject(projectPath);
      }

      // Open the newly created project
      return await this.openProject(projectPath);
    } catch (error) {
      throw new Error(`Failed to create project: ${error.message}`);
    }
  }

  private async createBasicProject(projectPath: string): Promise<void> {
    const projectName = path.basename(projectPath);
    
    // Create basic package.json
    const packageJson = {
      name: projectName.toLowerCase().replace(/\s+/g, '-'),
      version: '1.0.0',
      description: '',
      main: 'index.js',
      scripts: {
        start: 'node index.js',
        test: 'echo "Error: no test specified" && exit 1'
      },
      keywords: [],
      author: '',
      license: 'ISC'
    };

    await this.fs.writeFile(
      path.join(projectPath, 'package.json'),
      JSON.stringify(packageJson, null, 2)
    );

    // Create basic README.md
    const readme = `# ${projectName}

## Description
A new project created with KilatCode IDE.

## Getting Started
1. Install dependencies: \`npm install\`
2. Start the project: \`npm start\`

## Features
- Built with KilatCode IDE
- Ready for development

## License
ISC
`;

    await this.fs.writeFile(path.join(projectPath, 'README.md'), readme);

    // Create basic index.js
    const indexJs = `// Welcome to your new project!
console.log('Hello from ${projectName}!');

// Start building something amazing...
`;

    await this.fs.writeFile(path.join(projectPath, 'index.js'), indexJs);

    // Create .gitignore
    const gitignore = `node_modules/
dist/
build/
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.DS_Store
Thumbs.db
`;

    await this.fs.writeFile(path.join(projectPath, '.gitignore'), gitignore);
  }

  private async applyTemplate(projectPath: string, template: ProjectTemplate): Promise<void> {
    // Create files from template
    for (const file of template.files) {
      const filePath = path.join(projectPath, file.path);
      
      // Ensure directory exists
      await this.fs.ensureDirectory(path.dirname(filePath));
      
      // Process template variables if needed
      let content = file.content;
      if (file.template) {
        content = this.processTemplate(content, {
          projectName: path.basename(projectPath),
          projectPath: projectPath
        });
      }
      
      await this.fs.writeFile(filePath, content);
    }

    // Create package.json with template dependencies
    if (template.dependencies || template.devDependencies || template.scripts) {
      const packageJsonPath = path.join(projectPath, 'package.json');
      let packageJson: any = {};

      if (await this.fs.exists(packageJsonPath)) {
        const content = await this.fs.readFile(packageJsonPath);
        packageJson = JSON.parse(content);
      } else {
        packageJson = {
          name: path.basename(projectPath).toLowerCase().replace(/\s+/g, '-'),
          version: '1.0.0',
          description: template.description || ''
        };
      }

      if (template.dependencies) {
        packageJson.dependencies = { ...packageJson.dependencies, ...template.dependencies };
      }

      if (template.devDependencies) {
        packageJson.devDependencies = { ...packageJson.devDependencies, ...template.devDependencies };
      }

      if (template.scripts) {
        packageJson.scripts = { ...packageJson.scripts, ...template.scripts };
      }

      await this.fs.writeFile(packageJsonPath, JSON.stringify(packageJson, null, 2));
    }
  }

  private processTemplate(content: string, variables: Record<string, string>): string {
    let processed = content;
    
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      processed = processed.replace(regex, value);
    }
    
    return processed;
  }

  // Project configuration
  private async loadProjectConfig(projectPath: string): Promise<ProjectConfig | null> {
    try {
      const packageJsonPath = path.join(projectPath, 'package.json');
      
      if (await this.fs.exists(packageJsonPath)) {
        const content = await this.fs.readFile(packageJsonPath);
        const config = JSON.parse(content) as ProjectConfig;
        
        this.emit('projectConfigLoaded', config);
        return config;
      }
      
      return null;
    } catch (error) {
      console.warn('Failed to load project config:', error.message);
      return null;
    }
  }

  public async updateProjectConfig(config: Partial<ProjectConfig>): Promise<void> {
    if (!this.currentProject) {
      throw new Error('No project is currently open');
    }

    try {
      const packageJsonPath = path.join(this.currentProject.path, 'package.json');
      let existingConfig: ProjectConfig = {
        name: path.basename(this.currentProject.path),
        version: '1.0.0'
      };

      if (await this.fs.exists(packageJsonPath)) {
        const content = await this.fs.readFile(packageJsonPath);
        existingConfig = JSON.parse(content);
      }

      const updatedConfig = { ...existingConfig, ...config };
      
      await this.fs.writeFile(
        packageJsonPath,
        JSON.stringify(updatedConfig, null, 2)
      );

      this.emit('projectConfigUpdated', updatedConfig);
    } catch (error) {
      throw new Error(`Failed to update project config: ${error.message}`);
    }
  }

  // File watching
  private startWatching(projectPath: string): void {
    this.projectWatcher = this.fs.watchPath(projectPath, {
      ignored: [
        /(^|[\/\\])\../,
        /node_modules/,
        /\.git/,
        /dist/,
        /build/,
        /coverage/
      ]
    });

    this.fs.on('fileChanged', (event) => {
      this.emit('fileChanged', event);
    });
  }

  private stopWatching(): void {
    if (this.projectWatcher) {
      this.fs.unwatchPath(this.projectWatcher);
      this.projectWatcher = null;
    }
  }

  // Project analysis
  public async analyzeProject(projectPath?: string): Promise<{
    type: string;
    framework?: string;
    language: string;
    hasTests: boolean;
    hasLinting: boolean;
    hasTypeScript: boolean;
    dependencies: string[];
    devDependencies: string[];
  }> {
    const targetPath = projectPath || this.currentProject?.path;
    if (!targetPath) {
      throw new Error('No project path provided');
    }

    const analysis = {
      type: 'unknown',
      language: 'javascript',
      hasTests: false,
      hasLinting: false,
      hasTypeScript: false,
      dependencies: [] as string[],
      devDependencies: [] as string[]
    };

    try {
      // Check package.json
      const packageJsonPath = path.join(targetPath, 'package.json');
      if (await this.fs.exists(packageJsonPath)) {
        const content = await this.fs.readFile(packageJsonPath);
        const packageJson = JSON.parse(content);

        analysis.dependencies = Object.keys(packageJson.dependencies || {});
        analysis.devDependencies = Object.keys(packageJson.devDependencies || {});

        // Detect framework
        if (analysis.dependencies.includes('react')) {
          analysis.type = 'react';
          analysis.framework = 'React';
        } else if (analysis.dependencies.includes('vue')) {
          analysis.type = 'vue';
          analysis.framework = 'Vue.js';
        } else if (analysis.dependencies.includes('angular')) {
          analysis.type = 'angular';
          analysis.framework = 'Angular';
        } else if (analysis.dependencies.includes('express')) {
          analysis.type = 'node';
          analysis.framework = 'Express';
        }

        // Check for TypeScript
        analysis.hasTypeScript = 
          analysis.dependencies.includes('typescript') ||
          analysis.devDependencies.includes('typescript') ||
          await this.fs.exists(path.join(targetPath, 'tsconfig.json'));

        if (analysis.hasTypeScript) {
          analysis.language = 'typescript';
        }

        // Check for testing
        analysis.hasTests = 
          analysis.devDependencies.some(dep => 
            ['jest', 'mocha', 'jasmine', 'vitest', 'cypress'].includes(dep)
          );

        // Check for linting
        analysis.hasLinting = 
          analysis.devDependencies.some(dep => 
            ['eslint', 'tslint', 'prettier'].includes(dep)
          );
      }

      return analysis;
    } catch (error) {
      console.warn('Failed to analyze project:', error.message);
      return analysis;
    }
  }

  public async deleteProject(projectId: number): Promise<void> {
    try {
      const project = this.db.getProject(projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      // Close project if it's currently open
      if (this.currentProject?.id === projectId) {
        await this.closeProject();
      }

      // Remove from database
      this.db.deleteProject(projectId);

      this.emit('projectDeleted', project);
    } catch (error) {
      throw new Error(`Failed to delete project: ${error.message}`);
    }
  }

  public destroy(): void {
    this.stopWatching();
    this.removeAllListeners();
  }
}
