{"hash": "81511a73", "configHash": "f1e1a5ac", "lockfileHash": "2a0c1736", "browserHash": "e5315992", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "881ec644", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "18fb63ae", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "c14f5cd2", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "b505db74", "needsInterop": true}, "@monaco-editor/react": {"src": "../../@monaco-editor/react/dist/index.mjs", "file": "@monaco-editor_react.js", "fileHash": "5bd236de", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "069574f1", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "e683109b", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "1691b67e", "needsInterop": false}, "react-split-pane": {"src": "../../react-split-pane/dist/index.esm.js", "file": "react-split-pane.js", "fileHash": "ff0cf983", "needsInterop": false}}, "chunks": {"chunk-SB5BK2J2": {"file": "chunk-SB5BK2J2.js"}, "chunk-N6MYFXC3": {"file": "chunk-N6MYFXC3.js"}}}