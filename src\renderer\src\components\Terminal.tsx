import React, { useEffect, useRef, useState } from 'react';
import { Terminal as XTerm } from 'xterm';
import { FitAddon } from 'xterm-addon-fit';
import { WebLinksAddon } from 'xterm-addon-web-links';
import '../styles/Terminal.css';
import 'xterm/css/xterm.css';

interface TerminalProps {
  id?: number;
  cwd?: string;
  shell?: string;
  onClose?: () => void;
  onData?: (data: string) => void;
  className?: string;
}

const Terminal: React.FC<TerminalProps> = ({
  id: propId,
  cwd,
  shell,
  onClose,
  onData,
  className = ''
}) => {
  const terminalRef = useRef<HTMLDivElement>(null);
  const [terminal, setTerminal] = useState<XTerm | null>(null);
  const [fitAddon, setFitAddon] = useState<FitAddon | null>(null);
  const [terminalId, setTerminalId] = useState<number | undefined>(propId);
  const [isReady, setIsReady] = useState<boolean>(false);

  // Initialize terminal
  useEffect(() => {
    if (!terminalRef.current) return;

    // Create terminal instance
    const term = new XTerm({
      cursorBlink: true,
      cursorStyle: 'bar',
      fontFamily: '"Fira Code", "Cascadia Code", Consolas, monospace',
      fontSize: 14,
      theme: {
        background: '#0a0a0a',
        foreground: '#f8f8f8',
        cursor: '#00d4ff',
        cursorAccent: '#0a0a0a',
        selection: 'rgba(0, 212, 255, 0.3)',
        black: '#0a0a0a',
        red: '#ff5555',
        green: '#50fa7b',
        yellow: '#f1fa8c',
        blue: '#0099cc',
        magenta: '#bd93f9',
        cyan: '#00d4ff',
        white: '#f8f8f8',
        brightBlack: '#6272a4',
        brightRed: '#ff6e6e',
        brightGreen: '#69ff94',
        brightYellow: '#ffffa5',
        brightBlue: '#00bfff',
        brightMagenta: '#d6acff',
        brightCyan: '#a4ffff',
        brightWhite: '#ffffff'
      }
    });

    // Create fit addon
    const fit = new FitAddon();
    term.loadAddon(fit);

    // Create web links addon
    const webLinks = new WebLinksAddon();
    term.loadAddon(webLinks);

    // Open terminal
    term.open(terminalRef.current);
    fit.fit();

    // Set state
    setTerminal(term);
    setFitAddon(fit);
    setIsReady(true);

    // Handle terminal resize
    const handleResize = () => {
      if (fit) {
        fit.fit();
        if (terminalId) {
          const { cols, rows } = term;
          window.electron.terminal.resize(terminalId, cols, rows);
        }
      }
    };

    // Add event listeners
    window.addEventListener('resize', handleResize);
    term.onData(data => {
      if (terminalId) {
        window.electron.terminal.write(terminalId, data);
      }
      if (onData) {
        onData(data);
      }
    });

    // Clean up
    return () => {
      window.removeEventListener('resize', handleResize);
      term.dispose();
    };
  }, [terminalRef]);

  // Create terminal process if not provided
  useEffect(() => {
    if (!isReady || terminalId) return;

    const createTerminal = async () => {
      try {
        // Get terminal dimensions
        const cols = terminal?.cols || 80;
        const rows = terminal?.rows || 24;

        // Create terminal process
        const termProcess = await window.electron.terminal.create({
          cwd,
          shell,
          cols,
          rows
        });

        setTerminalId(termProcess.id);

        // Write welcome message
        terminal?.writeln('KilatCode Terminal');
        terminal?.writeln(`Shell: ${termProcess.shell}`);
        terminal?.writeln(`CWD: ${termProcess.cwd}`);
        terminal?.writeln('');
      } catch (error) {
        console.error('Failed to create terminal:', error);
        terminal?.writeln('\x1b[31mFailed to create terminal process\x1b[0m');
      }
    };

    createTerminal();
  }, [isReady, terminalId, cwd, shell, terminal]);

  // Handle terminal data events
  useEffect(() => {
    if (!terminalId) return;

    const handleTerminalData = (_: any, data: { id: number; data: string }) => {
      if (data.id === terminalId && terminal) {
        terminal.write(data.data);
      }
    };

    const handleTerminalExit = (_: any, data: { id: number; code: number }) => {
      if (data.id === terminalId && terminal) {
        terminal.writeln(`\r\n\x1b[33mProcess exited with code ${data.code}\x1b[0m`);
        terminal.writeln('\r\n\x1b[33mPress any key to close the terminal\x1b[0m');
        
        // Set up one-time handler to close terminal on key press
        const disposable = terminal.onData(() => {
          disposable.dispose();
          if (onClose) {
            onClose();
          }
        });
      }
    };

    const handleTerminalError = (_: any, data: { id: number; error: string }) => {
      if (data.id === terminalId && terminal) {
        terminal.writeln(`\r\n\x1b[31mError: ${data.error}\x1b[0m`);
      }
    };

    // Add event listeners
    const removeDataListener = window.electron.ipc.on('terminal-data', handleTerminalData);
    const removeExitListener = window.electron.ipc.on('terminal-exit', handleTerminalExit);
    const removeErrorListener = window.electron.ipc.on('terminal-error', handleTerminalError);

    // Clean up
    return () => {
      removeDataListener();
      removeExitListener();
      removeErrorListener();

      // Kill terminal process
      if (terminalId) {
        window.electron.terminal.kill(terminalId).catch(console.error);
      }
    };
  }, [terminalId, terminal, onClose]);

  // Handle terminal resize
  useEffect(() => {
    if (!fitAddon || !terminal || !terminalId) return;

    const handleResize = () => {
      fitAddon.fit();
      const { cols, rows } = terminal;
      window.electron.terminal.resize(terminalId, cols, rows);
    };

    // Initial fit
    handleResize();

    // Add resize observer
    const resizeObserver = new ResizeObserver(handleResize);
    if (terminalRef.current) {
      resizeObserver.observe(terminalRef.current);
    }

    // Clean up
    return () => {
      resizeObserver.disconnect();
    };
  }, [fitAddon, terminal, terminalId]);

  return (
    <div className={`terminal-container ${className}`}>
      <div className="terminal-header">
        <div className="terminal-title">
          {shell ? `${shell} (${terminalId})` : `Terminal ${terminalId || ''}`}
        </div>
        <div className="terminal-actions">
          <button className="terminal-action" onClick={onClose} title="Close Terminal">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <line x1="18" y1="6" x2="6" y2="18"/>
              <line x1="6" y1="6" x2="18" y2="18"/>
            </svg>
          </button>
        </div>
      </div>
      <div className="terminal" ref={terminalRef}></div>
    </div>
  );
};

export default Terminal;
