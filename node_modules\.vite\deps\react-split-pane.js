import {
  __commonJS,
  __toESM,
  require_react
} from "./chunk-N6MYFXC3.js";

// node_modules/prop-types/node_modules/react-is/cjs/react-is.development.js
var require_react_is_development = __commonJS({
  "node_modules/prop-types/node_modules/react-is/cjs/react-is.development.js"(exports) {
    "use strict";
    if (true) {
      (function() {
        "use strict";
        var hasSymbol = typeof Symbol === "function" && Symbol.for;
        var REACT_ELEMENT_TYPE = hasSymbol ? Symbol.for("react.element") : 60103;
        var REACT_PORTAL_TYPE = hasSymbol ? Symbol.for("react.portal") : 60106;
        var REACT_FRAGMENT_TYPE = hasSymbol ? Symbol.for("react.fragment") : 60107;
        var REACT_STRICT_MODE_TYPE = hasSymbol ? Symbol.for("react.strict_mode") : 60108;
        var REACT_PROFILER_TYPE = hasSymbol ? Symbol.for("react.profiler") : 60114;
        var REACT_PROVIDER_TYPE = hasSymbol ? Symbol.for("react.provider") : 60109;
        var REACT_CONTEXT_TYPE = hasSymbol ? Symbol.for("react.context") : 60110;
        var REACT_ASYNC_MODE_TYPE = hasSymbol ? Symbol.for("react.async_mode") : 60111;
        var REACT_CONCURRENT_MODE_TYPE = hasSymbol ? Symbol.for("react.concurrent_mode") : 60111;
        var REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for("react.forward_ref") : 60112;
        var REACT_SUSPENSE_TYPE = hasSymbol ? Symbol.for("react.suspense") : 60113;
        var REACT_SUSPENSE_LIST_TYPE = hasSymbol ? Symbol.for("react.suspense_list") : 60120;
        var REACT_MEMO_TYPE = hasSymbol ? Symbol.for("react.memo") : 60115;
        var REACT_LAZY_TYPE = hasSymbol ? Symbol.for("react.lazy") : 60116;
        var REACT_BLOCK_TYPE = hasSymbol ? Symbol.for("react.block") : 60121;
        var REACT_FUNDAMENTAL_TYPE = hasSymbol ? Symbol.for("react.fundamental") : 60117;
        var REACT_RESPONDER_TYPE = hasSymbol ? Symbol.for("react.responder") : 60118;
        var REACT_SCOPE_TYPE = hasSymbol ? Symbol.for("react.scope") : 60119;
        function isValidElementType(type) {
          return typeof type === "string" || typeof type === "function" || // Note: its typeof might be other than 'symbol' or 'number' if it's a polyfill.
          type === REACT_FRAGMENT_TYPE || type === REACT_CONCURRENT_MODE_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || typeof type === "object" && type !== null && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_RESPONDER_TYPE || type.$$typeof === REACT_SCOPE_TYPE || type.$$typeof === REACT_BLOCK_TYPE);
        }
        function typeOf(object) {
          if (typeof object === "object" && object !== null) {
            var $$typeof = object.$$typeof;
            switch ($$typeof) {
              case REACT_ELEMENT_TYPE:
                var type = object.type;
                switch (type) {
                  case REACT_ASYNC_MODE_TYPE:
                  case REACT_CONCURRENT_MODE_TYPE:
                  case REACT_FRAGMENT_TYPE:
                  case REACT_PROFILER_TYPE:
                  case REACT_STRICT_MODE_TYPE:
                  case REACT_SUSPENSE_TYPE:
                    return type;
                  default:
                    var $$typeofType = type && type.$$typeof;
                    switch ($$typeofType) {
                      case REACT_CONTEXT_TYPE:
                      case REACT_FORWARD_REF_TYPE:
                      case REACT_LAZY_TYPE:
                      case REACT_MEMO_TYPE:
                      case REACT_PROVIDER_TYPE:
                        return $$typeofType;
                      default:
                        return $$typeof;
                    }
                }
              case REACT_PORTAL_TYPE:
                return $$typeof;
            }
          }
          return void 0;
        }
        var AsyncMode = REACT_ASYNC_MODE_TYPE;
        var ConcurrentMode = REACT_CONCURRENT_MODE_TYPE;
        var ContextConsumer = REACT_CONTEXT_TYPE;
        var ContextProvider = REACT_PROVIDER_TYPE;
        var Element = REACT_ELEMENT_TYPE;
        var ForwardRef = REACT_FORWARD_REF_TYPE;
        var Fragment = REACT_FRAGMENT_TYPE;
        var Lazy = REACT_LAZY_TYPE;
        var Memo = REACT_MEMO_TYPE;
        var Portal = REACT_PORTAL_TYPE;
        var Profiler = REACT_PROFILER_TYPE;
        var StrictMode = REACT_STRICT_MODE_TYPE;
        var Suspense = REACT_SUSPENSE_TYPE;
        var hasWarnedAboutDeprecatedIsAsyncMode = false;
        function isAsyncMode(object) {
          {
            if (!hasWarnedAboutDeprecatedIsAsyncMode) {
              hasWarnedAboutDeprecatedIsAsyncMode = true;
              console["warn"]("The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 17+. Update your code to use ReactIs.isConcurrentMode() instead. It has the exact same API.");
            }
          }
          return isConcurrentMode(object) || typeOf(object) === REACT_ASYNC_MODE_TYPE;
        }
        function isConcurrentMode(object) {
          return typeOf(object) === REACT_CONCURRENT_MODE_TYPE;
        }
        function isContextConsumer(object) {
          return typeOf(object) === REACT_CONTEXT_TYPE;
        }
        function isContextProvider(object) {
          return typeOf(object) === REACT_PROVIDER_TYPE;
        }
        function isElement(object) {
          return typeof object === "object" && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;
        }
        function isForwardRef(object) {
          return typeOf(object) === REACT_FORWARD_REF_TYPE;
        }
        function isFragment(object) {
          return typeOf(object) === REACT_FRAGMENT_TYPE;
        }
        function isLazy(object) {
          return typeOf(object) === REACT_LAZY_TYPE;
        }
        function isMemo(object) {
          return typeOf(object) === REACT_MEMO_TYPE;
        }
        function isPortal(object) {
          return typeOf(object) === REACT_PORTAL_TYPE;
        }
        function isProfiler(object) {
          return typeOf(object) === REACT_PROFILER_TYPE;
        }
        function isStrictMode(object) {
          return typeOf(object) === REACT_STRICT_MODE_TYPE;
        }
        function isSuspense(object) {
          return typeOf(object) === REACT_SUSPENSE_TYPE;
        }
        exports.AsyncMode = AsyncMode;
        exports.ConcurrentMode = ConcurrentMode;
        exports.ContextConsumer = ContextConsumer;
        exports.ContextProvider = ContextProvider;
        exports.Element = Element;
        exports.ForwardRef = ForwardRef;
        exports.Fragment = Fragment;
        exports.Lazy = Lazy;
        exports.Memo = Memo;
        exports.Portal = Portal;
        exports.Profiler = Profiler;
        exports.StrictMode = StrictMode;
        exports.Suspense = Suspense;
        exports.isAsyncMode = isAsyncMode;
        exports.isConcurrentMode = isConcurrentMode;
        exports.isContextConsumer = isContextConsumer;
        exports.isContextProvider = isContextProvider;
        exports.isElement = isElement;
        exports.isForwardRef = isForwardRef;
        exports.isFragment = isFragment;
        exports.isLazy = isLazy;
        exports.isMemo = isMemo;
        exports.isPortal = isPortal;
        exports.isProfiler = isProfiler;
        exports.isStrictMode = isStrictMode;
        exports.isSuspense = isSuspense;
        exports.isValidElementType = isValidElementType;
        exports.typeOf = typeOf;
      })();
    }
  }
});

// node_modules/prop-types/node_modules/react-is/index.js
var require_react_is = __commonJS({
  "node_modules/prop-types/node_modules/react-is/index.js"(exports, module) {
    "use strict";
    if (false) {
      module.exports = null;
    } else {
      module.exports = require_react_is_development();
    }
  }
});

// node_modules/object-assign/index.js
var require_object_assign = __commonJS({
  "node_modules/object-assign/index.js"(exports, module) {
    "use strict";
    var getOwnPropertySymbols = Object.getOwnPropertySymbols;
    var hasOwnProperty = Object.prototype.hasOwnProperty;
    var propIsEnumerable = Object.prototype.propertyIsEnumerable;
    function toObject(val) {
      if (val === null || val === void 0) {
        throw new TypeError("Object.assign cannot be called with null or undefined");
      }
      return Object(val);
    }
    function shouldUseNative() {
      try {
        if (!Object.assign) {
          return false;
        }
        var test1 = new String("abc");
        test1[5] = "de";
        if (Object.getOwnPropertyNames(test1)[0] === "5") {
          return false;
        }
        var test2 = {};
        for (var i = 0; i < 10; i++) {
          test2["_" + String.fromCharCode(i)] = i;
        }
        var order2 = Object.getOwnPropertyNames(test2).map(function(n) {
          return test2[n];
        });
        if (order2.join("") !== "0123456789") {
          return false;
        }
        var test3 = {};
        "abcdefghijklmnopqrst".split("").forEach(function(letter) {
          test3[letter] = letter;
        });
        if (Object.keys(Object.assign({}, test3)).join("") !== "abcdefghijklmnopqrst") {
          return false;
        }
        return true;
      } catch (err) {
        return false;
      }
    }
    module.exports = shouldUseNative() ? Object.assign : function(target, source) {
      var from;
      var to = toObject(target);
      var symbols;
      for (var s = 1; s < arguments.length; s++) {
        from = Object(arguments[s]);
        for (var key in from) {
          if (hasOwnProperty.call(from, key)) {
            to[key] = from[key];
          }
        }
        if (getOwnPropertySymbols) {
          symbols = getOwnPropertySymbols(from);
          for (var i = 0; i < symbols.length; i++) {
            if (propIsEnumerable.call(from, symbols[i])) {
              to[symbols[i]] = from[symbols[i]];
            }
          }
        }
      }
      return to;
    };
  }
});

// node_modules/prop-types/lib/ReactPropTypesSecret.js
var require_ReactPropTypesSecret = __commonJS({
  "node_modules/prop-types/lib/ReactPropTypesSecret.js"(exports, module) {
    "use strict";
    var ReactPropTypesSecret = "SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";
    module.exports = ReactPropTypesSecret;
  }
});

// node_modules/prop-types/lib/has.js
var require_has = __commonJS({
  "node_modules/prop-types/lib/has.js"(exports, module) {
    module.exports = Function.call.bind(Object.prototype.hasOwnProperty);
  }
});

// node_modules/prop-types/checkPropTypes.js
var require_checkPropTypes = __commonJS({
  "node_modules/prop-types/checkPropTypes.js"(exports, module) {
    "use strict";
    var printWarning = function() {
    };
    if (true) {
      ReactPropTypesSecret = require_ReactPropTypesSecret();
      loggedTypeFailures = {};
      has = require_has();
      printWarning = function(text) {
        var message = "Warning: " + text;
        if (typeof console !== "undefined") {
          console.error(message);
        }
        try {
          throw new Error(message);
        } catch (x) {
        }
      };
    }
    var ReactPropTypesSecret;
    var loggedTypeFailures;
    var has;
    function checkPropTypes(typeSpecs, values, location, componentName, getStack) {
      if (true) {
        for (var typeSpecName in typeSpecs) {
          if (has(typeSpecs, typeSpecName)) {
            var error;
            try {
              if (typeof typeSpecs[typeSpecName] !== "function") {
                var err = Error(
                  (componentName || "React class") + ": " + location + " type `" + typeSpecName + "` is invalid; it must be a function, usually from the `prop-types` package, but received `" + typeof typeSpecs[typeSpecName] + "`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`."
                );
                err.name = "Invariant Violation";
                throw err;
              }
              error = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, ReactPropTypesSecret);
            } catch (ex) {
              error = ex;
            }
            if (error && !(error instanceof Error)) {
              printWarning(
                (componentName || "React class") + ": type specification of " + location + " `" + typeSpecName + "` is invalid; the type checker function must return `null` or an `Error` but returned a " + typeof error + ". You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument)."
              );
            }
            if (error instanceof Error && !(error.message in loggedTypeFailures)) {
              loggedTypeFailures[error.message] = true;
              var stack = getStack ? getStack() : "";
              printWarning(
                "Failed " + location + " type: " + error.message + (stack != null ? stack : "")
              );
            }
          }
        }
      }
    }
    checkPropTypes.resetWarningCache = function() {
      if (true) {
        loggedTypeFailures = {};
      }
    };
    module.exports = checkPropTypes;
  }
});

// node_modules/prop-types/factoryWithTypeCheckers.js
var require_factoryWithTypeCheckers = __commonJS({
  "node_modules/prop-types/factoryWithTypeCheckers.js"(exports, module) {
    "use strict";
    var ReactIs = require_react_is();
    var assign = require_object_assign();
    var ReactPropTypesSecret = require_ReactPropTypesSecret();
    var has = require_has();
    var checkPropTypes = require_checkPropTypes();
    var printWarning = function() {
    };
    if (true) {
      printWarning = function(text) {
        var message = "Warning: " + text;
        if (typeof console !== "undefined") {
          console.error(message);
        }
        try {
          throw new Error(message);
        } catch (x) {
        }
      };
    }
    function emptyFunctionThatReturnsNull() {
      return null;
    }
    module.exports = function(isValidElement, throwOnDirectAccess) {
      var ITERATOR_SYMBOL = typeof Symbol === "function" && Symbol.iterator;
      var FAUX_ITERATOR_SYMBOL = "@@iterator";
      function getIteratorFn(maybeIterable) {
        var iteratorFn = maybeIterable && (ITERATOR_SYMBOL && maybeIterable[ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL]);
        if (typeof iteratorFn === "function") {
          return iteratorFn;
        }
      }
      var ANONYMOUS = "<<anonymous>>";
      var ReactPropTypes = {
        array: createPrimitiveTypeChecker("array"),
        bigint: createPrimitiveTypeChecker("bigint"),
        bool: createPrimitiveTypeChecker("boolean"),
        func: createPrimitiveTypeChecker("function"),
        number: createPrimitiveTypeChecker("number"),
        object: createPrimitiveTypeChecker("object"),
        string: createPrimitiveTypeChecker("string"),
        symbol: createPrimitiveTypeChecker("symbol"),
        any: createAnyTypeChecker(),
        arrayOf: createArrayOfTypeChecker,
        element: createElementTypeChecker(),
        elementType: createElementTypeTypeChecker(),
        instanceOf: createInstanceTypeChecker,
        node: createNodeChecker(),
        objectOf: createObjectOfTypeChecker,
        oneOf: createEnumTypeChecker,
        oneOfType: createUnionTypeChecker,
        shape: createShapeTypeChecker,
        exact: createStrictShapeTypeChecker
      };
      function is(x, y) {
        if (x === y) {
          return x !== 0 || 1 / x === 1 / y;
        } else {
          return x !== x && y !== y;
        }
      }
      function PropTypeError(message, data) {
        this.message = message;
        this.data = data && typeof data === "object" ? data : {};
        this.stack = "";
      }
      PropTypeError.prototype = Error.prototype;
      function createChainableTypeChecker(validate) {
        if (true) {
          var manualPropTypeCallCache = {};
          var manualPropTypeWarningCount = 0;
        }
        function checkType(isRequired, props, propName, componentName, location, propFullName, secret) {
          componentName = componentName || ANONYMOUS;
          propFullName = propFullName || propName;
          if (secret !== ReactPropTypesSecret) {
            if (throwOnDirectAccess) {
              var err = new Error(
                "Calling PropTypes validators directly is not supported by the `prop-types` package. Use `PropTypes.checkPropTypes()` to call them. Read more at http://fb.me/use-check-prop-types"
              );
              err.name = "Invariant Violation";
              throw err;
            } else if (typeof console !== "undefined") {
              var cacheKey = componentName + ":" + propName;
              if (!manualPropTypeCallCache[cacheKey] && // Avoid spamming the console because they are often not actionable except for lib authors
              manualPropTypeWarningCount < 3) {
                printWarning(
                  "You are manually calling a React.PropTypes validation function for the `" + propFullName + "` prop on `" + componentName + "`. This is deprecated and will throw in the standalone `prop-types` package. You may be seeing this warning due to a third-party PropTypes library. See https://fb.me/react-warning-dont-call-proptypes for details."
                );
                manualPropTypeCallCache[cacheKey] = true;
                manualPropTypeWarningCount++;
              }
            }
          }
          if (props[propName] == null) {
            if (isRequired) {
              if (props[propName] === null) {
                return new PropTypeError("The " + location + " `" + propFullName + "` is marked as required " + ("in `" + componentName + "`, but its value is `null`."));
              }
              return new PropTypeError("The " + location + " `" + propFullName + "` is marked as required in " + ("`" + componentName + "`, but its value is `undefined`."));
            }
            return null;
          } else {
            return validate(props, propName, componentName, location, propFullName);
          }
        }
        var chainedCheckType = checkType.bind(null, false);
        chainedCheckType.isRequired = checkType.bind(null, true);
        return chainedCheckType;
      }
      function createPrimitiveTypeChecker(expectedType) {
        function validate(props, propName, componentName, location, propFullName, secret) {
          var propValue = props[propName];
          var propType = getPropType(propValue);
          if (propType !== expectedType) {
            var preciseType = getPreciseType(propValue);
            return new PropTypeError(
              "Invalid " + location + " `" + propFullName + "` of type " + ("`" + preciseType + "` supplied to `" + componentName + "`, expected ") + ("`" + expectedType + "`."),
              { expectedType }
            );
          }
          return null;
        }
        return createChainableTypeChecker(validate);
      }
      function createAnyTypeChecker() {
        return createChainableTypeChecker(emptyFunctionThatReturnsNull);
      }
      function createArrayOfTypeChecker(typeChecker) {
        function validate(props, propName, componentName, location, propFullName) {
          if (typeof typeChecker !== "function") {
            return new PropTypeError("Property `" + propFullName + "` of component `" + componentName + "` has invalid PropType notation inside arrayOf.");
          }
          var propValue = props[propName];
          if (!Array.isArray(propValue)) {
            var propType = getPropType(propValue);
            return new PropTypeError("Invalid " + location + " `" + propFullName + "` of type " + ("`" + propType + "` supplied to `" + componentName + "`, expected an array."));
          }
          for (var i = 0; i < propValue.length; i++) {
            var error = typeChecker(propValue, i, componentName, location, propFullName + "[" + i + "]", ReactPropTypesSecret);
            if (error instanceof Error) {
              return error;
            }
          }
          return null;
        }
        return createChainableTypeChecker(validate);
      }
      function createElementTypeChecker() {
        function validate(props, propName, componentName, location, propFullName) {
          var propValue = props[propName];
          if (!isValidElement(propValue)) {
            var propType = getPropType(propValue);
            return new PropTypeError("Invalid " + location + " `" + propFullName + "` of type " + ("`" + propType + "` supplied to `" + componentName + "`, expected a single ReactElement."));
          }
          return null;
        }
        return createChainableTypeChecker(validate);
      }
      function createElementTypeTypeChecker() {
        function validate(props, propName, componentName, location, propFullName) {
          var propValue = props[propName];
          if (!ReactIs.isValidElementType(propValue)) {
            var propType = getPropType(propValue);
            return new PropTypeError("Invalid " + location + " `" + propFullName + "` of type " + ("`" + propType + "` supplied to `" + componentName + "`, expected a single ReactElement type."));
          }
          return null;
        }
        return createChainableTypeChecker(validate);
      }
      function createInstanceTypeChecker(expectedClass) {
        function validate(props, propName, componentName, location, propFullName) {
          if (!(props[propName] instanceof expectedClass)) {
            var expectedClassName = expectedClass.name || ANONYMOUS;
            var actualClassName = getClassName(props[propName]);
            return new PropTypeError("Invalid " + location + " `" + propFullName + "` of type " + ("`" + actualClassName + "` supplied to `" + componentName + "`, expected ") + ("instance of `" + expectedClassName + "`."));
          }
          return null;
        }
        return createChainableTypeChecker(validate);
      }
      function createEnumTypeChecker(expectedValues) {
        if (!Array.isArray(expectedValues)) {
          if (true) {
            if (arguments.length > 1) {
              printWarning(
                "Invalid arguments supplied to oneOf, expected an array, got " + arguments.length + " arguments. A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z])."
              );
            } else {
              printWarning("Invalid argument supplied to oneOf, expected an array.");
            }
          }
          return emptyFunctionThatReturnsNull;
        }
        function validate(props, propName, componentName, location, propFullName) {
          var propValue = props[propName];
          for (var i = 0; i < expectedValues.length; i++) {
            if (is(propValue, expectedValues[i])) {
              return null;
            }
          }
          var valuesString = JSON.stringify(expectedValues, function replacer(key, value) {
            var type = getPreciseType(value);
            if (type === "symbol") {
              return String(value);
            }
            return value;
          });
          return new PropTypeError("Invalid " + location + " `" + propFullName + "` of value `" + String(propValue) + "` " + ("supplied to `" + componentName + "`, expected one of " + valuesString + "."));
        }
        return createChainableTypeChecker(validate);
      }
      function createObjectOfTypeChecker(typeChecker) {
        function validate(props, propName, componentName, location, propFullName) {
          if (typeof typeChecker !== "function") {
            return new PropTypeError("Property `" + propFullName + "` of component `" + componentName + "` has invalid PropType notation inside objectOf.");
          }
          var propValue = props[propName];
          var propType = getPropType(propValue);
          if (propType !== "object") {
            return new PropTypeError("Invalid " + location + " `" + propFullName + "` of type " + ("`" + propType + "` supplied to `" + componentName + "`, expected an object."));
          }
          for (var key in propValue) {
            if (has(propValue, key)) {
              var error = typeChecker(propValue, key, componentName, location, propFullName + "." + key, ReactPropTypesSecret);
              if (error instanceof Error) {
                return error;
              }
            }
          }
          return null;
        }
        return createChainableTypeChecker(validate);
      }
      function createUnionTypeChecker(arrayOfTypeCheckers) {
        if (!Array.isArray(arrayOfTypeCheckers)) {
          true ? printWarning("Invalid argument supplied to oneOfType, expected an instance of array.") : void 0;
          return emptyFunctionThatReturnsNull;
        }
        for (var i = 0; i < arrayOfTypeCheckers.length; i++) {
          var checker = arrayOfTypeCheckers[i];
          if (typeof checker !== "function") {
            printWarning(
              "Invalid argument supplied to oneOfType. Expected an array of check functions, but received " + getPostfixForTypeWarning(checker) + " at index " + i + "."
            );
            return emptyFunctionThatReturnsNull;
          }
        }
        function validate(props, propName, componentName, location, propFullName) {
          var expectedTypes = [];
          for (var i2 = 0; i2 < arrayOfTypeCheckers.length; i2++) {
            var checker2 = arrayOfTypeCheckers[i2];
            var checkerResult = checker2(props, propName, componentName, location, propFullName, ReactPropTypesSecret);
            if (checkerResult == null) {
              return null;
            }
            if (checkerResult.data && has(checkerResult.data, "expectedType")) {
              expectedTypes.push(checkerResult.data.expectedType);
            }
          }
          var expectedTypesMessage = expectedTypes.length > 0 ? ", expected one of type [" + expectedTypes.join(", ") + "]" : "";
          return new PropTypeError("Invalid " + location + " `" + propFullName + "` supplied to " + ("`" + componentName + "`" + expectedTypesMessage + "."));
        }
        return createChainableTypeChecker(validate);
      }
      function createNodeChecker() {
        function validate(props, propName, componentName, location, propFullName) {
          if (!isNode(props[propName])) {
            return new PropTypeError("Invalid " + location + " `" + propFullName + "` supplied to " + ("`" + componentName + "`, expected a ReactNode."));
          }
          return null;
        }
        return createChainableTypeChecker(validate);
      }
      function invalidValidatorError(componentName, location, propFullName, key, type) {
        return new PropTypeError(
          (componentName || "React class") + ": " + location + " type `" + propFullName + "." + key + "` is invalid; it must be a function, usually from the `prop-types` package, but received `" + type + "`."
        );
      }
      function createShapeTypeChecker(shapeTypes) {
        function validate(props, propName, componentName, location, propFullName) {
          var propValue = props[propName];
          var propType = getPropType(propValue);
          if (propType !== "object") {
            return new PropTypeError("Invalid " + location + " `" + propFullName + "` of type `" + propType + "` " + ("supplied to `" + componentName + "`, expected `object`."));
          }
          for (var key in shapeTypes) {
            var checker = shapeTypes[key];
            if (typeof checker !== "function") {
              return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));
            }
            var error = checker(propValue, key, componentName, location, propFullName + "." + key, ReactPropTypesSecret);
            if (error) {
              return error;
            }
          }
          return null;
        }
        return createChainableTypeChecker(validate);
      }
      function createStrictShapeTypeChecker(shapeTypes) {
        function validate(props, propName, componentName, location, propFullName) {
          var propValue = props[propName];
          var propType = getPropType(propValue);
          if (propType !== "object") {
            return new PropTypeError("Invalid " + location + " `" + propFullName + "` of type `" + propType + "` " + ("supplied to `" + componentName + "`, expected `object`."));
          }
          var allKeys = assign({}, props[propName], shapeTypes);
          for (var key in allKeys) {
            var checker = shapeTypes[key];
            if (has(shapeTypes, key) && typeof checker !== "function") {
              return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));
            }
            if (!checker) {
              return new PropTypeError(
                "Invalid " + location + " `" + propFullName + "` key `" + key + "` supplied to `" + componentName + "`.\nBad object: " + JSON.stringify(props[propName], null, "  ") + "\nValid keys: " + JSON.stringify(Object.keys(shapeTypes), null, "  ")
              );
            }
            var error = checker(propValue, key, componentName, location, propFullName + "." + key, ReactPropTypesSecret);
            if (error) {
              return error;
            }
          }
          return null;
        }
        return createChainableTypeChecker(validate);
      }
      function isNode(propValue) {
        switch (typeof propValue) {
          case "number":
          case "string":
          case "undefined":
            return true;
          case "boolean":
            return !propValue;
          case "object":
            if (Array.isArray(propValue)) {
              return propValue.every(isNode);
            }
            if (propValue === null || isValidElement(propValue)) {
              return true;
            }
            var iteratorFn = getIteratorFn(propValue);
            if (iteratorFn) {
              var iterator = iteratorFn.call(propValue);
              var step;
              if (iteratorFn !== propValue.entries) {
                while (!(step = iterator.next()).done) {
                  if (!isNode(step.value)) {
                    return false;
                  }
                }
              } else {
                while (!(step = iterator.next()).done) {
                  var entry = step.value;
                  if (entry) {
                    if (!isNode(entry[1])) {
                      return false;
                    }
                  }
                }
              }
            } else {
              return false;
            }
            return true;
          default:
            return false;
        }
      }
      function isSymbol(propType, propValue) {
        if (propType === "symbol") {
          return true;
        }
        if (!propValue) {
          return false;
        }
        if (propValue["@@toStringTag"] === "Symbol") {
          return true;
        }
        if (typeof Symbol === "function" && propValue instanceof Symbol) {
          return true;
        }
        return false;
      }
      function getPropType(propValue) {
        var propType = typeof propValue;
        if (Array.isArray(propValue)) {
          return "array";
        }
        if (propValue instanceof RegExp) {
          return "object";
        }
        if (isSymbol(propType, propValue)) {
          return "symbol";
        }
        return propType;
      }
      function getPreciseType(propValue) {
        if (typeof propValue === "undefined" || propValue === null) {
          return "" + propValue;
        }
        var propType = getPropType(propValue);
        if (propType === "object") {
          if (propValue instanceof Date) {
            return "date";
          } else if (propValue instanceof RegExp) {
            return "regexp";
          }
        }
        return propType;
      }
      function getPostfixForTypeWarning(value) {
        var type = getPreciseType(value);
        switch (type) {
          case "array":
          case "object":
            return "an " + type;
          case "boolean":
          case "date":
          case "regexp":
            return "a " + type;
          default:
            return type;
        }
      }
      function getClassName(propValue) {
        if (!propValue.constructor || !propValue.constructor.name) {
          return ANONYMOUS;
        }
        return propValue.constructor.name;
      }
      ReactPropTypes.checkPropTypes = checkPropTypes;
      ReactPropTypes.resetWarningCache = checkPropTypes.resetWarningCache;
      ReactPropTypes.PropTypes = ReactPropTypes;
      return ReactPropTypes;
    };
  }
});

// node_modules/prop-types/index.js
var require_prop_types = __commonJS({
  "node_modules/prop-types/index.js"(exports, module) {
    if (true) {
      ReactIs = require_react_is();
      throwOnDirectAccess = true;
      module.exports = require_factoryWithTypeCheckers()(ReactIs.isElement, throwOnDirectAccess);
    } else {
      module.exports = null();
    }
    var ReactIs;
    var throwOnDirectAccess;
  }
});

// node_modules/react-style-proptype/src/css-properties.js
var require_css_properties = __commonJS({
  "node_modules/react-style-proptype/src/css-properties.js"(exports, module) {
    module.exports = [
      "alignContent",
      "MozAlignContent",
      "WebkitAlignContent",
      "MSAlignContent",
      "OAlignContent",
      "alignItems",
      "MozAlignItems",
      "WebkitAlignItems",
      "MSAlignItems",
      "OAlignItems",
      "alignSelf",
      "MozAlignSelf",
      "WebkitAlignSelf",
      "MSAlignSelf",
      "OAlignSelf",
      "all",
      "MozAll",
      "WebkitAll",
      "MSAll",
      "OAll",
      "animation",
      "MozAnimation",
      "WebkitAnimation",
      "MSAnimation",
      "OAnimation",
      "animationDelay",
      "MozAnimationDelay",
      "WebkitAnimationDelay",
      "MSAnimationDelay",
      "OAnimationDelay",
      "animationDirection",
      "MozAnimationDirection",
      "WebkitAnimationDirection",
      "MSAnimationDirection",
      "OAnimationDirection",
      "animationDuration",
      "MozAnimationDuration",
      "WebkitAnimationDuration",
      "MSAnimationDuration",
      "OAnimationDuration",
      "animationFillMode",
      "MozAnimationFillMode",
      "WebkitAnimationFillMode",
      "MSAnimationFillMode",
      "OAnimationFillMode",
      "animationIterationCount",
      "MozAnimationIterationCount",
      "WebkitAnimationIterationCount",
      "MSAnimationIterationCount",
      "OAnimationIterationCount",
      "animationName",
      "MozAnimationName",
      "WebkitAnimationName",
      "MSAnimationName",
      "OAnimationName",
      "animationPlayState",
      "MozAnimationPlayState",
      "WebkitAnimationPlayState",
      "MSAnimationPlayState",
      "OAnimationPlayState",
      "animationTimingFunction",
      "MozAnimationTimingFunction",
      "WebkitAnimationTimingFunction",
      "MSAnimationTimingFunction",
      "OAnimationTimingFunction",
      "backfaceVisibility",
      "MozBackfaceVisibility",
      "WebkitBackfaceVisibility",
      "MSBackfaceVisibility",
      "OBackfaceVisibility",
      "background",
      "MozBackground",
      "WebkitBackground",
      "MSBackground",
      "OBackground",
      "backgroundAttachment",
      "MozBackgroundAttachment",
      "WebkitBackgroundAttachment",
      "MSBackgroundAttachment",
      "OBackgroundAttachment",
      "backgroundBlendMode",
      "MozBackgroundBlendMode",
      "WebkitBackgroundBlendMode",
      "MSBackgroundBlendMode",
      "OBackgroundBlendMode",
      "backgroundClip",
      "MozBackgroundClip",
      "WebkitBackgroundClip",
      "MSBackgroundClip",
      "OBackgroundClip",
      "backgroundColor",
      "MozBackgroundColor",
      "WebkitBackgroundColor",
      "MSBackgroundColor",
      "OBackgroundColor",
      "backgroundImage",
      "MozBackgroundImage",
      "WebkitBackgroundImage",
      "MSBackgroundImage",
      "OBackgroundImage",
      "backgroundOrigin",
      "MozBackgroundOrigin",
      "WebkitBackgroundOrigin",
      "MSBackgroundOrigin",
      "OBackgroundOrigin",
      "backgroundPosition",
      "MozBackgroundPosition",
      "WebkitBackgroundPosition",
      "MSBackgroundPosition",
      "OBackgroundPosition",
      "backgroundRepeat",
      "MozBackgroundRepeat",
      "WebkitBackgroundRepeat",
      "MSBackgroundRepeat",
      "OBackgroundRepeat",
      "backgroundSize",
      "MozBackgroundSize",
      "WebkitBackgroundSize",
      "MSBackgroundSize",
      "OBackgroundSize",
      "blockSize",
      "MozBlockSize",
      "WebkitBlockSize",
      "MSBlockSize",
      "OBlockSize",
      "border",
      "MozBorder",
      "WebkitBorder",
      "MSBorder",
      "OBorder",
      "borderBlockEnd",
      "MozBorderBlockEnd",
      "WebkitBorderBlockEnd",
      "MSBorderBlockEnd",
      "OBorderBlockEnd",
      "borderBlockEndColor",
      "MozBorderBlockEndColor",
      "WebkitBorderBlockEndColor",
      "MSBorderBlockEndColor",
      "OBorderBlockEndColor",
      "borderBlockEndStyle",
      "MozBorderBlockEndStyle",
      "WebkitBorderBlockEndStyle",
      "MSBorderBlockEndStyle",
      "OBorderBlockEndStyle",
      "borderBlockEndWidth",
      "MozBorderBlockEndWidth",
      "WebkitBorderBlockEndWidth",
      "MSBorderBlockEndWidth",
      "OBorderBlockEndWidth",
      "borderBlockStart",
      "MozBorderBlockStart",
      "WebkitBorderBlockStart",
      "MSBorderBlockStart",
      "OBorderBlockStart",
      "borderBlockStartColor",
      "MozBorderBlockStartColor",
      "WebkitBorderBlockStartColor",
      "MSBorderBlockStartColor",
      "OBorderBlockStartColor",
      "borderBlockStartStyle",
      "MozBorderBlockStartStyle",
      "WebkitBorderBlockStartStyle",
      "MSBorderBlockStartStyle",
      "OBorderBlockStartStyle",
      "borderBlockStartWidth",
      "MozBorderBlockStartWidth",
      "WebkitBorderBlockStartWidth",
      "MSBorderBlockStartWidth",
      "OBorderBlockStartWidth",
      "borderBottom",
      "MozBorderBottom",
      "WebkitBorderBottom",
      "MSBorderBottom",
      "OBorderBottom",
      "borderBottomColor",
      "MozBorderBottomColor",
      "WebkitBorderBottomColor",
      "MSBorderBottomColor",
      "OBorderBottomColor",
      "borderBottomLeftRadius",
      "MozBorderBottomLeftRadius",
      "WebkitBorderBottomLeftRadius",
      "MSBorderBottomLeftRadius",
      "OBorderBottomLeftRadius",
      "borderBottomRightRadius",
      "MozBorderBottomRightRadius",
      "WebkitBorderBottomRightRadius",
      "MSBorderBottomRightRadius",
      "OBorderBottomRightRadius",
      "borderBottomStyle",
      "MozBorderBottomStyle",
      "WebkitBorderBottomStyle",
      "MSBorderBottomStyle",
      "OBorderBottomStyle",
      "borderBottomWidth",
      "MozBorderBottomWidth",
      "WebkitBorderBottomWidth",
      "MSBorderBottomWidth",
      "OBorderBottomWidth",
      "borderCollapse",
      "MozBorderCollapse",
      "WebkitBorderCollapse",
      "MSBorderCollapse",
      "OBorderCollapse",
      "borderColor",
      "MozBorderColor",
      "WebkitBorderColor",
      "MSBorderColor",
      "OBorderColor",
      "borderImage",
      "MozBorderImage",
      "WebkitBorderImage",
      "MSBorderImage",
      "OBorderImage",
      "borderImageOutset",
      "MozBorderImageOutset",
      "WebkitBorderImageOutset",
      "MSBorderImageOutset",
      "OBorderImageOutset",
      "borderImageRepeat",
      "MozBorderImageRepeat",
      "WebkitBorderImageRepeat",
      "MSBorderImageRepeat",
      "OBorderImageRepeat",
      "borderImageSlice",
      "MozBorderImageSlice",
      "WebkitBorderImageSlice",
      "MSBorderImageSlice",
      "OBorderImageSlice",
      "borderImageSource",
      "MozBorderImageSource",
      "WebkitBorderImageSource",
      "MSBorderImageSource",
      "OBorderImageSource",
      "borderImageWidth",
      "MozBorderImageWidth",
      "WebkitBorderImageWidth",
      "MSBorderImageWidth",
      "OBorderImageWidth",
      "borderInlineEnd",
      "MozBorderInlineEnd",
      "WebkitBorderInlineEnd",
      "MSBorderInlineEnd",
      "OBorderInlineEnd",
      "borderInlineEndColor",
      "MozBorderInlineEndColor",
      "WebkitBorderInlineEndColor",
      "MSBorderInlineEndColor",
      "OBorderInlineEndColor",
      "borderInlineEndStyle",
      "MozBorderInlineEndStyle",
      "WebkitBorderInlineEndStyle",
      "MSBorderInlineEndStyle",
      "OBorderInlineEndStyle",
      "borderInlineEndWidth",
      "MozBorderInlineEndWidth",
      "WebkitBorderInlineEndWidth",
      "MSBorderInlineEndWidth",
      "OBorderInlineEndWidth",
      "borderInlineStart",
      "MozBorderInlineStart",
      "WebkitBorderInlineStart",
      "MSBorderInlineStart",
      "OBorderInlineStart",
      "borderInlineStartColor",
      "MozBorderInlineStartColor",
      "WebkitBorderInlineStartColor",
      "MSBorderInlineStartColor",
      "OBorderInlineStartColor",
      "borderInlineStartStyle",
      "MozBorderInlineStartStyle",
      "WebkitBorderInlineStartStyle",
      "MSBorderInlineStartStyle",
      "OBorderInlineStartStyle",
      "borderInlineStartWidth",
      "MozBorderInlineStartWidth",
      "WebkitBorderInlineStartWidth",
      "MSBorderInlineStartWidth",
      "OBorderInlineStartWidth",
      "borderLeft",
      "MozBorderLeft",
      "WebkitBorderLeft",
      "MSBorderLeft",
      "OBorderLeft",
      "borderLeftColor",
      "MozBorderLeftColor",
      "WebkitBorderLeftColor",
      "MSBorderLeftColor",
      "OBorderLeftColor",
      "borderLeftStyle",
      "MozBorderLeftStyle",
      "WebkitBorderLeftStyle",
      "MSBorderLeftStyle",
      "OBorderLeftStyle",
      "borderLeftWidth",
      "MozBorderLeftWidth",
      "WebkitBorderLeftWidth",
      "MSBorderLeftWidth",
      "OBorderLeftWidth",
      "borderRadius",
      "MozBorderRadius",
      "WebkitBorderRadius",
      "MSBorderRadius",
      "OBorderRadius",
      "borderRight",
      "MozBorderRight",
      "WebkitBorderRight",
      "MSBorderRight",
      "OBorderRight",
      "borderRightColor",
      "MozBorderRightColor",
      "WebkitBorderRightColor",
      "MSBorderRightColor",
      "OBorderRightColor",
      "borderRightStyle",
      "MozBorderRightStyle",
      "WebkitBorderRightStyle",
      "MSBorderRightStyle",
      "OBorderRightStyle",
      "borderRightWidth",
      "MozBorderRightWidth",
      "WebkitBorderRightWidth",
      "MSBorderRightWidth",
      "OBorderRightWidth",
      "borderSpacing",
      "MozBorderSpacing",
      "WebkitBorderSpacing",
      "MSBorderSpacing",
      "OBorderSpacing",
      "borderStyle",
      "MozBorderStyle",
      "WebkitBorderStyle",
      "MSBorderStyle",
      "OBorderStyle",
      "borderTop",
      "MozBorderTop",
      "WebkitBorderTop",
      "MSBorderTop",
      "OBorderTop",
      "borderTopColor",
      "MozBorderTopColor",
      "WebkitBorderTopColor",
      "MSBorderTopColor",
      "OBorderTopColor",
      "borderTopLeftRadius",
      "MozBorderTopLeftRadius",
      "WebkitBorderTopLeftRadius",
      "MSBorderTopLeftRadius",
      "OBorderTopLeftRadius",
      "borderTopRightRadius",
      "MozBorderTopRightRadius",
      "WebkitBorderTopRightRadius",
      "MSBorderTopRightRadius",
      "OBorderTopRightRadius",
      "borderTopStyle",
      "MozBorderTopStyle",
      "WebkitBorderTopStyle",
      "MSBorderTopStyle",
      "OBorderTopStyle",
      "borderTopWidth",
      "MozBorderTopWidth",
      "WebkitBorderTopWidth",
      "MSBorderTopWidth",
      "OBorderTopWidth",
      "borderWidth",
      "MozBorderWidth",
      "WebkitBorderWidth",
      "MSBorderWidth",
      "OBorderWidth",
      "bottom",
      "MozBottom",
      "WebkitBottom",
      "MSBottom",
      "OBottom",
      "boxDecorationBreak",
      "MozBoxDecorationBreak",
      "WebkitBoxDecorationBreak",
      "MSBoxDecorationBreak",
      "OBoxDecorationBreak",
      "boxShadow",
      "MozBoxShadow",
      "WebkitBoxShadow",
      "MSBoxShadow",
      "OBoxShadow",
      "boxSizing",
      "MozBoxSizing",
      "WebkitBoxSizing",
      "MSBoxSizing",
      "OBoxSizing",
      "breakAfter",
      "MozBreakAfter",
      "WebkitBreakAfter",
      "MSBreakAfter",
      "OBreakAfter",
      "breakBefore",
      "MozBreakBefore",
      "WebkitBreakBefore",
      "MSBreakBefore",
      "OBreakBefore",
      "breakInside",
      "MozBreakInside",
      "WebkitBreakInside",
      "MSBreakInside",
      "OBreakInside",
      "captionSide",
      "MozCaptionSide",
      "WebkitCaptionSide",
      "MSCaptionSide",
      "OCaptionSide",
      "caretColor",
      "MozCaretColor",
      "WebkitCaretColor",
      "MSCaretColor",
      "OCaretColor",
      "ch",
      "MozCh",
      "WebkitCh",
      "MSCh",
      "OCh",
      "clear",
      "MozClear",
      "WebkitClear",
      "MSClear",
      "OClear",
      "clip",
      "MozClip",
      "WebkitClip",
      "MSClip",
      "OClip",
      "clipPath",
      "MozClipPath",
      "WebkitClipPath",
      "MSClipPath",
      "OClipPath",
      "cm",
      "MozCm",
      "WebkitCm",
      "MSCm",
      "OCm",
      "color",
      "MozColor",
      "WebkitColor",
      "MSColor",
      "OColor",
      "columnCount",
      "MozColumnCount",
      "WebkitColumnCount",
      "MSColumnCount",
      "OColumnCount",
      "columnFill",
      "MozColumnFill",
      "WebkitColumnFill",
      "MSColumnFill",
      "OColumnFill",
      "columnGap",
      "MozColumnGap",
      "WebkitColumnGap",
      "MSColumnGap",
      "OColumnGap",
      "columnRule",
      "MozColumnRule",
      "WebkitColumnRule",
      "MSColumnRule",
      "OColumnRule",
      "columnRuleColor",
      "MozColumnRuleColor",
      "WebkitColumnRuleColor",
      "MSColumnRuleColor",
      "OColumnRuleColor",
      "columnRuleStyle",
      "MozColumnRuleStyle",
      "WebkitColumnRuleStyle",
      "MSColumnRuleStyle",
      "OColumnRuleStyle",
      "columnRuleWidth",
      "MozColumnRuleWidth",
      "WebkitColumnRuleWidth",
      "MSColumnRuleWidth",
      "OColumnRuleWidth",
      "columnSpan",
      "MozColumnSpan",
      "WebkitColumnSpan",
      "MSColumnSpan",
      "OColumnSpan",
      "columnWidth",
      "MozColumnWidth",
      "WebkitColumnWidth",
      "MSColumnWidth",
      "OColumnWidth",
      "columns",
      "MozColumns",
      "WebkitColumns",
      "MSColumns",
      "OColumns",
      "content",
      "MozContent",
      "WebkitContent",
      "MSContent",
      "OContent",
      "counterIncrement",
      "MozCounterIncrement",
      "WebkitCounterIncrement",
      "MSCounterIncrement",
      "OCounterIncrement",
      "counterReset",
      "MozCounterReset",
      "WebkitCounterReset",
      "MSCounterReset",
      "OCounterReset",
      "cursor",
      "MozCursor",
      "WebkitCursor",
      "MSCursor",
      "OCursor",
      "deg",
      "MozDeg",
      "WebkitDeg",
      "MSDeg",
      "ODeg",
      "direction",
      "MozDirection",
      "WebkitDirection",
      "MSDirection",
      "ODirection",
      "display",
      "MozDisplay",
      "WebkitDisplay",
      "MSDisplay",
      "ODisplay",
      "dpcm",
      "MozDpcm",
      "WebkitDpcm",
      "MSDpcm",
      "ODpcm",
      "dpi",
      "MozDpi",
      "WebkitDpi",
      "MSDpi",
      "ODpi",
      "dppx",
      "MozDppx",
      "WebkitDppx",
      "MSDppx",
      "ODppx",
      "em",
      "MozEm",
      "WebkitEm",
      "MSEm",
      "OEm",
      "emptyCells",
      "MozEmptyCells",
      "WebkitEmptyCells",
      "MSEmptyCells",
      "OEmptyCells",
      "ex",
      "MozEx",
      "WebkitEx",
      "MSEx",
      "OEx",
      "filter",
      "MozFilter",
      "WebkitFilter",
      "MSFilter",
      "OFilter",
      "flexBasis",
      "MozFlexBasis",
      "WebkitFlexBasis",
      "MSFlexBasis",
      "OFlexBasis",
      "flexDirection",
      "MozFlexDirection",
      "WebkitFlexDirection",
      "MSFlexDirection",
      "OFlexDirection",
      "flexFlow",
      "MozFlexFlow",
      "WebkitFlexFlow",
      "MSFlexFlow",
      "OFlexFlow",
      "flexGrow",
      "MozFlexGrow",
      "WebkitFlexGrow",
      "MSFlexGrow",
      "OFlexGrow",
      "flexShrink",
      "MozFlexShrink",
      "WebkitFlexShrink",
      "MSFlexShrink",
      "OFlexShrink",
      "flexWrap",
      "MozFlexWrap",
      "WebkitFlexWrap",
      "MSFlexWrap",
      "OFlexWrap",
      "float",
      "MozFloat",
      "WebkitFloat",
      "MSFloat",
      "OFloat",
      "font",
      "MozFont",
      "WebkitFont",
      "MSFont",
      "OFont",
      "fontFamily",
      "MozFontFamily",
      "WebkitFontFamily",
      "MSFontFamily",
      "OFontFamily",
      "fontFeatureSettings",
      "MozFontFeatureSettings",
      "WebkitFontFeatureSettings",
      "MSFontFeatureSettings",
      "OFontFeatureSettings",
      "fontKerning",
      "MozFontKerning",
      "WebkitFontKerning",
      "MSFontKerning",
      "OFontKerning",
      "fontLanguageOverride",
      "MozFontLanguageOverride",
      "WebkitFontLanguageOverride",
      "MSFontLanguageOverride",
      "OFontLanguageOverride",
      "fontSize",
      "MozFontSize",
      "WebkitFontSize",
      "MSFontSize",
      "OFontSize",
      "fontSizeAdjust",
      "MozFontSizeAdjust",
      "WebkitFontSizeAdjust",
      "MSFontSizeAdjust",
      "OFontSizeAdjust",
      "fontStretch",
      "MozFontStretch",
      "WebkitFontStretch",
      "MSFontStretch",
      "OFontStretch",
      "fontStyle",
      "MozFontStyle",
      "WebkitFontStyle",
      "MSFontStyle",
      "OFontStyle",
      "fontSynthesis",
      "MozFontSynthesis",
      "WebkitFontSynthesis",
      "MSFontSynthesis",
      "OFontSynthesis",
      "fontVariant",
      "MozFontVariant",
      "WebkitFontVariant",
      "MSFontVariant",
      "OFontVariant",
      "fontVariantAlternates",
      "MozFontVariantAlternates",
      "WebkitFontVariantAlternates",
      "MSFontVariantAlternates",
      "OFontVariantAlternates",
      "fontVariantCaps",
      "MozFontVariantCaps",
      "WebkitFontVariantCaps",
      "MSFontVariantCaps",
      "OFontVariantCaps",
      "fontVariantEastAsian",
      "MozFontVariantEastAsian",
      "WebkitFontVariantEastAsian",
      "MSFontVariantEastAsian",
      "OFontVariantEastAsian",
      "fontVariantLigatures",
      "MozFontVariantLigatures",
      "WebkitFontVariantLigatures",
      "MSFontVariantLigatures",
      "OFontVariantLigatures",
      "fontVariantNumeric",
      "MozFontVariantNumeric",
      "WebkitFontVariantNumeric",
      "MSFontVariantNumeric",
      "OFontVariantNumeric",
      "fontVariantPosition",
      "MozFontVariantPosition",
      "WebkitFontVariantPosition",
      "MSFontVariantPosition",
      "OFontVariantPosition",
      "fontWeight",
      "MozFontWeight",
      "WebkitFontWeight",
      "MSFontWeight",
      "OFontWeight",
      "fr",
      "MozFr",
      "WebkitFr",
      "MSFr",
      "OFr",
      "grad",
      "MozGrad",
      "WebkitGrad",
      "MSGrad",
      "OGrad",
      "grid",
      "MozGrid",
      "WebkitGrid",
      "MSGrid",
      "OGrid",
      "gridArea",
      "MozGridArea",
      "WebkitGridArea",
      "MSGridArea",
      "OGridArea",
      "gridAutoColumns",
      "MozGridAutoColumns",
      "WebkitGridAutoColumns",
      "MSGridAutoColumns",
      "OGridAutoColumns",
      "gridAutoFlow",
      "MozGridAutoFlow",
      "WebkitGridAutoFlow",
      "MSGridAutoFlow",
      "OGridAutoFlow",
      "gridAutoRows",
      "MozGridAutoRows",
      "WebkitGridAutoRows",
      "MSGridAutoRows",
      "OGridAutoRows",
      "gridColumn",
      "MozGridColumn",
      "WebkitGridColumn",
      "MSGridColumn",
      "OGridColumn",
      "gridColumnEnd",
      "MozGridColumnEnd",
      "WebkitGridColumnEnd",
      "MSGridColumnEnd",
      "OGridColumnEnd",
      "gridColumnGap",
      "MozGridColumnGap",
      "WebkitGridColumnGap",
      "MSGridColumnGap",
      "OGridColumnGap",
      "gridColumnStart",
      "MozGridColumnStart",
      "WebkitGridColumnStart",
      "MSGridColumnStart",
      "OGridColumnStart",
      "gridGap",
      "MozGridGap",
      "WebkitGridGap",
      "MSGridGap",
      "OGridGap",
      "gridRow",
      "MozGridRow",
      "WebkitGridRow",
      "MSGridRow",
      "OGridRow",
      "gridRowEnd",
      "MozGridRowEnd",
      "WebkitGridRowEnd",
      "MSGridRowEnd",
      "OGridRowEnd",
      "gridRowGap",
      "MozGridRowGap",
      "WebkitGridRowGap",
      "MSGridRowGap",
      "OGridRowGap",
      "gridRowStart",
      "MozGridRowStart",
      "WebkitGridRowStart",
      "MSGridRowStart",
      "OGridRowStart",
      "gridTemplate",
      "MozGridTemplate",
      "WebkitGridTemplate",
      "MSGridTemplate",
      "OGridTemplate",
      "gridTemplateAreas",
      "MozGridTemplateAreas",
      "WebkitGridTemplateAreas",
      "MSGridTemplateAreas",
      "OGridTemplateAreas",
      "gridTemplateColumns",
      "MozGridTemplateColumns",
      "WebkitGridTemplateColumns",
      "MSGridTemplateColumns",
      "OGridTemplateColumns",
      "gridTemplateRows",
      "MozGridTemplateRows",
      "WebkitGridTemplateRows",
      "MSGridTemplateRows",
      "OGridTemplateRows",
      "height",
      "MozHeight",
      "WebkitHeight",
      "MSHeight",
      "OHeight",
      "hyphens",
      "MozHyphens",
      "WebkitHyphens",
      "MSHyphens",
      "OHyphens",
      "hz",
      "MozHz",
      "WebkitHz",
      "MSHz",
      "OHz",
      "imageOrientation",
      "MozImageOrientation",
      "WebkitImageOrientation",
      "MSImageOrientation",
      "OImageOrientation",
      "imageRendering",
      "MozImageRendering",
      "WebkitImageRendering",
      "MSImageRendering",
      "OImageRendering",
      "imageResolution",
      "MozImageResolution",
      "WebkitImageResolution",
      "MSImageResolution",
      "OImageResolution",
      "imeMode",
      "MozImeMode",
      "WebkitImeMode",
      "MSImeMode",
      "OImeMode",
      "in",
      "MozIn",
      "WebkitIn",
      "MSIn",
      "OIn",
      "inherit",
      "MozInherit",
      "WebkitInherit",
      "MSInherit",
      "OInherit",
      "initial",
      "MozInitial",
      "WebkitInitial",
      "MSInitial",
      "OInitial",
      "inlineSize",
      "MozInlineSize",
      "WebkitInlineSize",
      "MSInlineSize",
      "OInlineSize",
      "isolation",
      "MozIsolation",
      "WebkitIsolation",
      "MSIsolation",
      "OIsolation",
      "justifyContent",
      "MozJustifyContent",
      "WebkitJustifyContent",
      "MSJustifyContent",
      "OJustifyContent",
      "khz",
      "MozKhz",
      "WebkitKhz",
      "MSKhz",
      "OKhz",
      "left",
      "MozLeft",
      "WebkitLeft",
      "MSLeft",
      "OLeft",
      "letterSpacing",
      "MozLetterSpacing",
      "WebkitLetterSpacing",
      "MSLetterSpacing",
      "OLetterSpacing",
      "lineBreak",
      "MozLineBreak",
      "WebkitLineBreak",
      "MSLineBreak",
      "OLineBreak",
      "lineHeight",
      "MozLineHeight",
      "WebkitLineHeight",
      "MSLineHeight",
      "OLineHeight",
      "listStyle",
      "MozListStyle",
      "WebkitListStyle",
      "MSListStyle",
      "OListStyle",
      "listStyleImage",
      "MozListStyleImage",
      "WebkitListStyleImage",
      "MSListStyleImage",
      "OListStyleImage",
      "listStylePosition",
      "MozListStylePosition",
      "WebkitListStylePosition",
      "MSListStylePosition",
      "OListStylePosition",
      "listStyleType",
      "MozListStyleType",
      "WebkitListStyleType",
      "MSListStyleType",
      "OListStyleType",
      "margin",
      "MozMargin",
      "WebkitMargin",
      "MSMargin",
      "OMargin",
      "marginBlockEnd",
      "MozMarginBlockEnd",
      "WebkitMarginBlockEnd",
      "MSMarginBlockEnd",
      "OMarginBlockEnd",
      "marginBlockStart",
      "MozMarginBlockStart",
      "WebkitMarginBlockStart",
      "MSMarginBlockStart",
      "OMarginBlockStart",
      "marginBottom",
      "MozMarginBottom",
      "WebkitMarginBottom",
      "MSMarginBottom",
      "OMarginBottom",
      "marginInlineEnd",
      "MozMarginInlineEnd",
      "WebkitMarginInlineEnd",
      "MSMarginInlineEnd",
      "OMarginInlineEnd",
      "marginInlineStart",
      "MozMarginInlineStart",
      "WebkitMarginInlineStart",
      "MSMarginInlineStart",
      "OMarginInlineStart",
      "marginLeft",
      "MozMarginLeft",
      "WebkitMarginLeft",
      "MSMarginLeft",
      "OMarginLeft",
      "marginRight",
      "MozMarginRight",
      "WebkitMarginRight",
      "MSMarginRight",
      "OMarginRight",
      "marginTop",
      "MozMarginTop",
      "WebkitMarginTop",
      "MSMarginTop",
      "OMarginTop",
      "mask",
      "MozMask",
      "WebkitMask",
      "MSMask",
      "OMask",
      "maskClip",
      "MozMaskClip",
      "WebkitMaskClip",
      "MSMaskClip",
      "OMaskClip",
      "maskComposite",
      "MozMaskComposite",
      "WebkitMaskComposite",
      "MSMaskComposite",
      "OMaskComposite",
      "maskImage",
      "MozMaskImage",
      "WebkitMaskImage",
      "MSMaskImage",
      "OMaskImage",
      "maskMode",
      "MozMaskMode",
      "WebkitMaskMode",
      "MSMaskMode",
      "OMaskMode",
      "maskOrigin",
      "MozMaskOrigin",
      "WebkitMaskOrigin",
      "MSMaskOrigin",
      "OMaskOrigin",
      "maskPosition",
      "MozMaskPosition",
      "WebkitMaskPosition",
      "MSMaskPosition",
      "OMaskPosition",
      "maskRepeat",
      "MozMaskRepeat",
      "WebkitMaskRepeat",
      "MSMaskRepeat",
      "OMaskRepeat",
      "maskSize",
      "MozMaskSize",
      "WebkitMaskSize",
      "MSMaskSize",
      "OMaskSize",
      "maskType",
      "MozMaskType",
      "WebkitMaskType",
      "MSMaskType",
      "OMaskType",
      "maxHeight",
      "MozMaxHeight",
      "WebkitMaxHeight",
      "MSMaxHeight",
      "OMaxHeight",
      "maxWidth",
      "MozMaxWidth",
      "WebkitMaxWidth",
      "MSMaxWidth",
      "OMaxWidth",
      "minBlockSize",
      "MozMinBlockSize",
      "WebkitMinBlockSize",
      "MSMinBlockSize",
      "OMinBlockSize",
      "minHeight",
      "MozMinHeight",
      "WebkitMinHeight",
      "MSMinHeight",
      "OMinHeight",
      "minInlineSize",
      "MozMinInlineSize",
      "WebkitMinInlineSize",
      "MSMinInlineSize",
      "OMinInlineSize",
      "minWidth",
      "MozMinWidth",
      "WebkitMinWidth",
      "MSMinWidth",
      "OMinWidth",
      "mixBlendMode",
      "MozMixBlendMode",
      "WebkitMixBlendMode",
      "MSMixBlendMode",
      "OMixBlendMode",
      "mm",
      "MozMm",
      "WebkitMm",
      "MSMm",
      "OMm",
      "ms",
      "MozMs",
      "WebkitMs",
      "MSMs",
      "OMs",
      "objectFit",
      "MozObjectFit",
      "WebkitObjectFit",
      "MSObjectFit",
      "OObjectFit",
      "objectPosition",
      "MozObjectPosition",
      "WebkitObjectPosition",
      "MSObjectPosition",
      "OObjectPosition",
      "offsetBlockEnd",
      "MozOffsetBlockEnd",
      "WebkitOffsetBlockEnd",
      "MSOffsetBlockEnd",
      "OOffsetBlockEnd",
      "offsetBlockStart",
      "MozOffsetBlockStart",
      "WebkitOffsetBlockStart",
      "MSOffsetBlockStart",
      "OOffsetBlockStart",
      "offsetInlineEnd",
      "MozOffsetInlineEnd",
      "WebkitOffsetInlineEnd",
      "MSOffsetInlineEnd",
      "OOffsetInlineEnd",
      "offsetInlineStart",
      "MozOffsetInlineStart",
      "WebkitOffsetInlineStart",
      "MSOffsetInlineStart",
      "OOffsetInlineStart",
      "opacity",
      "MozOpacity",
      "WebkitOpacity",
      "MSOpacity",
      "OOpacity",
      "order",
      "MozOrder",
      "WebkitOrder",
      "MSOrder",
      "OOrder",
      "orphans",
      "MozOrphans",
      "WebkitOrphans",
      "MSOrphans",
      "OOrphans",
      "outline",
      "MozOutline",
      "WebkitOutline",
      "MSOutline",
      "OOutline",
      "outlineColor",
      "MozOutlineColor",
      "WebkitOutlineColor",
      "MSOutlineColor",
      "OOutlineColor",
      "outlineOffset",
      "MozOutlineOffset",
      "WebkitOutlineOffset",
      "MSOutlineOffset",
      "OOutlineOffset",
      "outlineStyle",
      "MozOutlineStyle",
      "WebkitOutlineStyle",
      "MSOutlineStyle",
      "OOutlineStyle",
      "outlineWidth",
      "MozOutlineWidth",
      "WebkitOutlineWidth",
      "MSOutlineWidth",
      "OOutlineWidth",
      "overflow",
      "MozOverflow",
      "WebkitOverflow",
      "MSOverflow",
      "OOverflow",
      "overflowWrap",
      "MozOverflowWrap",
      "WebkitOverflowWrap",
      "MSOverflowWrap",
      "OOverflowWrap",
      "overflowX",
      "MozOverflowX",
      "WebkitOverflowX",
      "MSOverflowX",
      "OOverflowX",
      "overflowY",
      "MozOverflowY",
      "WebkitOverflowY",
      "MSOverflowY",
      "OOverflowY",
      "padding",
      "MozPadding",
      "WebkitPadding",
      "MSPadding",
      "OPadding",
      "paddingBlockEnd",
      "MozPaddingBlockEnd",
      "WebkitPaddingBlockEnd",
      "MSPaddingBlockEnd",
      "OPaddingBlockEnd",
      "paddingBlockStart",
      "MozPaddingBlockStart",
      "WebkitPaddingBlockStart",
      "MSPaddingBlockStart",
      "OPaddingBlockStart",
      "paddingBottom",
      "MozPaddingBottom",
      "WebkitPaddingBottom",
      "MSPaddingBottom",
      "OPaddingBottom",
      "paddingInlineEnd",
      "MozPaddingInlineEnd",
      "WebkitPaddingInlineEnd",
      "MSPaddingInlineEnd",
      "OPaddingInlineEnd",
      "paddingInlineStart",
      "MozPaddingInlineStart",
      "WebkitPaddingInlineStart",
      "MSPaddingInlineStart",
      "OPaddingInlineStart",
      "paddingLeft",
      "MozPaddingLeft",
      "WebkitPaddingLeft",
      "MSPaddingLeft",
      "OPaddingLeft",
      "paddingRight",
      "MozPaddingRight",
      "WebkitPaddingRight",
      "MSPaddingRight",
      "OPaddingRight",
      "paddingTop",
      "MozPaddingTop",
      "WebkitPaddingTop",
      "MSPaddingTop",
      "OPaddingTop",
      "pageBreakAfter",
      "MozPageBreakAfter",
      "WebkitPageBreakAfter",
      "MSPageBreakAfter",
      "OPageBreakAfter",
      "pageBreakBefore",
      "MozPageBreakBefore",
      "WebkitPageBreakBefore",
      "MSPageBreakBefore",
      "OPageBreakBefore",
      "pageBreakInside",
      "MozPageBreakInside",
      "WebkitPageBreakInside",
      "MSPageBreakInside",
      "OPageBreakInside",
      "pc",
      "MozPc",
      "WebkitPc",
      "MSPc",
      "OPc",
      "perspective",
      "MozPerspective",
      "WebkitPerspective",
      "MSPerspective",
      "OPerspective",
      "perspectiveOrigin",
      "MozPerspectiveOrigin",
      "WebkitPerspectiveOrigin",
      "MSPerspectiveOrigin",
      "OPerspectiveOrigin",
      "pointerEvents",
      "MozPointerEvents",
      "WebkitPointerEvents",
      "MSPointerEvents",
      "OPointerEvents",
      "position",
      "MozPosition",
      "WebkitPosition",
      "MSPosition",
      "OPosition",
      "pt",
      "MozPt",
      "WebkitPt",
      "MSPt",
      "OPt",
      "px",
      "MozPx",
      "WebkitPx",
      "MSPx",
      "OPx",
      "q",
      "MozQ",
      "WebkitQ",
      "MSQ",
      "OQ",
      "quotes",
      "MozQuotes",
      "WebkitQuotes",
      "MSQuotes",
      "OQuotes",
      "rad",
      "MozRad",
      "WebkitRad",
      "MSRad",
      "ORad",
      "rem",
      "MozRem",
      "WebkitRem",
      "MSRem",
      "ORem",
      "resize",
      "MozResize",
      "WebkitResize",
      "MSResize",
      "OResize",
      "revert",
      "MozRevert",
      "WebkitRevert",
      "MSRevert",
      "ORevert",
      "right",
      "MozRight",
      "WebkitRight",
      "MSRight",
      "ORight",
      "rubyAlign",
      "MozRubyAlign",
      "WebkitRubyAlign",
      "MSRubyAlign",
      "ORubyAlign",
      "rubyMerge",
      "MozRubyMerge",
      "WebkitRubyMerge",
      "MSRubyMerge",
      "ORubyMerge",
      "rubyPosition",
      "MozRubyPosition",
      "WebkitRubyPosition",
      "MSRubyPosition",
      "ORubyPosition",
      "s",
      "MozS",
      "WebkitS",
      "MSS",
      "OS",
      "scrollBehavior",
      "MozScrollBehavior",
      "WebkitScrollBehavior",
      "MSScrollBehavior",
      "OScrollBehavior",
      "scrollSnapCoordinate",
      "MozScrollSnapCoordinate",
      "WebkitScrollSnapCoordinate",
      "MSScrollSnapCoordinate",
      "OScrollSnapCoordinate",
      "scrollSnapDestination",
      "MozScrollSnapDestination",
      "WebkitScrollSnapDestination",
      "MSScrollSnapDestination",
      "OScrollSnapDestination",
      "scrollSnapType",
      "MozScrollSnapType",
      "WebkitScrollSnapType",
      "MSScrollSnapType",
      "OScrollSnapType",
      "shapeImageThreshold",
      "MozShapeImageThreshold",
      "WebkitShapeImageThreshold",
      "MSShapeImageThreshold",
      "OShapeImageThreshold",
      "shapeMargin",
      "MozShapeMargin",
      "WebkitShapeMargin",
      "MSShapeMargin",
      "OShapeMargin",
      "shapeOutside",
      "MozShapeOutside",
      "WebkitShapeOutside",
      "MSShapeOutside",
      "OShapeOutside",
      "tabSize",
      "MozTabSize",
      "WebkitTabSize",
      "MSTabSize",
      "OTabSize",
      "tableLayout",
      "MozTableLayout",
      "WebkitTableLayout",
      "MSTableLayout",
      "OTableLayout",
      "textAlign",
      "MozTextAlign",
      "WebkitTextAlign",
      "MSTextAlign",
      "OTextAlign",
      "textAlignLast",
      "MozTextAlignLast",
      "WebkitTextAlignLast",
      "MSTextAlignLast",
      "OTextAlignLast",
      "textCombineUpright",
      "MozTextCombineUpright",
      "WebkitTextCombineUpright",
      "MSTextCombineUpright",
      "OTextCombineUpright",
      "textDecoration",
      "MozTextDecoration",
      "WebkitTextDecoration",
      "MSTextDecoration",
      "OTextDecoration",
      "textDecorationColor",
      "MozTextDecorationColor",
      "WebkitTextDecorationColor",
      "MSTextDecorationColor",
      "OTextDecorationColor",
      "textDecorationLine",
      "MozTextDecorationLine",
      "WebkitTextDecorationLine",
      "MSTextDecorationLine",
      "OTextDecorationLine",
      "textDecorationStyle",
      "MozTextDecorationStyle",
      "WebkitTextDecorationStyle",
      "MSTextDecorationStyle",
      "OTextDecorationStyle",
      "textEmphasis",
      "MozTextEmphasis",
      "WebkitTextEmphasis",
      "MSTextEmphasis",
      "OTextEmphasis",
      "textEmphasisColor",
      "MozTextEmphasisColor",
      "WebkitTextEmphasisColor",
      "MSTextEmphasisColor",
      "OTextEmphasisColor",
      "textEmphasisPosition",
      "MozTextEmphasisPosition",
      "WebkitTextEmphasisPosition",
      "MSTextEmphasisPosition",
      "OTextEmphasisPosition",
      "textEmphasisStyle",
      "MozTextEmphasisStyle",
      "WebkitTextEmphasisStyle",
      "MSTextEmphasisStyle",
      "OTextEmphasisStyle",
      "textIndent",
      "MozTextIndent",
      "WebkitTextIndent",
      "MSTextIndent",
      "OTextIndent",
      "textOrientation",
      "MozTextOrientation",
      "WebkitTextOrientation",
      "MSTextOrientation",
      "OTextOrientation",
      "textOverflow",
      "MozTextOverflow",
      "WebkitTextOverflow",
      "MSTextOverflow",
      "OTextOverflow",
      "textRendering",
      "MozTextRendering",
      "WebkitTextRendering",
      "MSTextRendering",
      "OTextRendering",
      "textShadow",
      "MozTextShadow",
      "WebkitTextShadow",
      "MSTextShadow",
      "OTextShadow",
      "textTransform",
      "MozTextTransform",
      "WebkitTextTransform",
      "MSTextTransform",
      "OTextTransform",
      "textUnderlinePosition",
      "MozTextUnderlinePosition",
      "WebkitTextUnderlinePosition",
      "MSTextUnderlinePosition",
      "OTextUnderlinePosition",
      "top",
      "MozTop",
      "WebkitTop",
      "MSTop",
      "OTop",
      "touchAction",
      "MozTouchAction",
      "WebkitTouchAction",
      "MSTouchAction",
      "OTouchAction",
      "transform",
      "MozTransform",
      "WebkitTransform",
      "msTransform",
      "OTransform",
      "transformBox",
      "MozTransformBox",
      "WebkitTransformBox",
      "MSTransformBox",
      "OTransformBox",
      "transformOrigin",
      "MozTransformOrigin",
      "WebkitTransformOrigin",
      "MSTransformOrigin",
      "OTransformOrigin",
      "transformStyle",
      "MozTransformStyle",
      "WebkitTransformStyle",
      "MSTransformStyle",
      "OTransformStyle",
      "transition",
      "MozTransition",
      "WebkitTransition",
      "MSTransition",
      "OTransition",
      "transitionDelay",
      "MozTransitionDelay",
      "WebkitTransitionDelay",
      "MSTransitionDelay",
      "OTransitionDelay",
      "transitionDuration",
      "MozTransitionDuration",
      "WebkitTransitionDuration",
      "MSTransitionDuration",
      "OTransitionDuration",
      "transitionProperty",
      "MozTransitionProperty",
      "WebkitTransitionProperty",
      "MSTransitionProperty",
      "OTransitionProperty",
      "transitionTimingFunction",
      "MozTransitionTimingFunction",
      "WebkitTransitionTimingFunction",
      "MSTransitionTimingFunction",
      "OTransitionTimingFunction",
      "turn",
      "MozTurn",
      "WebkitTurn",
      "MSTurn",
      "OTurn",
      "unicodeBidi",
      "MozUnicodeBidi",
      "WebkitUnicodeBidi",
      "MSUnicodeBidi",
      "OUnicodeBidi",
      "unset",
      "MozUnset",
      "WebkitUnset",
      "MSUnset",
      "OUnset",
      "verticalAlign",
      "MozVerticalAlign",
      "WebkitVerticalAlign",
      "MSVerticalAlign",
      "OVerticalAlign",
      "vh",
      "MozVh",
      "WebkitVh",
      "MSVh",
      "OVh",
      "visibility",
      "MozVisibility",
      "WebkitVisibility",
      "MSVisibility",
      "OVisibility",
      "vmax",
      "MozVmax",
      "WebkitVmax",
      "MSVmax",
      "OVmax",
      "vmin",
      "MozVmin",
      "WebkitVmin",
      "MSVmin",
      "OVmin",
      "vw",
      "MozVw",
      "WebkitVw",
      "MSVw",
      "OVw",
      "whiteSpace",
      "MozWhiteSpace",
      "WebkitWhiteSpace",
      "MSWhiteSpace",
      "OWhiteSpace",
      "widows",
      "MozWidows",
      "WebkitWidows",
      "MSWidows",
      "OWidows",
      "width",
      "MozWidth",
      "WebkitWidth",
      "MSWidth",
      "OWidth",
      "willChange",
      "MozWillChange",
      "WebkitWillChange",
      "MSWillChange",
      "OWillChange",
      "wordBreak",
      "MozWordBreak",
      "WebkitWordBreak",
      "MSWordBreak",
      "OWordBreak",
      "wordSpacing",
      "MozWordSpacing",
      "WebkitWordSpacing",
      "MSWordSpacing",
      "OWordSpacing",
      "wordWrap",
      "MozWordWrap",
      "WebkitWordWrap",
      "MSWordWrap",
      "OWordWrap",
      "writingMode",
      "MozWritingMode",
      "WebkitWritingMode",
      "MSWritingMode",
      "OWritingMode",
      "zIndex",
      "MozZIndex",
      "WebkitZIndex",
      "MSZIndex",
      "OZIndex",
      "fontSize",
      "MozFontSize",
      "WebkitFontSize",
      "MSFontSize",
      "OFontSize",
      "flex",
      "MozFlex",
      "WebkitFlex",
      "MSFlex",
      "OFlex",
      "fr",
      "MozFr",
      "WebkitFr",
      "MSFr",
      "OFr",
      "overflowScrolling",
      "MozOverflowScrolling",
      "WebkitOverflowScrolling",
      "MSOverflowScrolling",
      "OOverflowScrolling",
      "userSelect",
      "MozUserSelect",
      "WebkitUserSelect",
      "MSUserSelect",
      "OUserSelect"
    ];
  }
});

// node_modules/react-style-proptype/src/index.js
var require_src = __commonJS({
  "node_modules/react-style-proptype/src/index.js"(exports, module) {
    var properties = require_css_properties();
    var PropTypes2 = require_prop_types();
    module.exports = function(props, propName, componentName) {
      var styles = props[propName];
      if (!styles) {
        return;
      }
      var failures = [];
      Object.keys(styles).forEach(function(styleKey) {
        if (properties.indexOf(styleKey) === -1) {
          failures.push(styleKey);
        }
      });
      if (failures.length) {
        throw new Error("Prop " + propName + " passed to " + componentName + ". Has invalid keys " + failures.join(", "));
      }
    };
    module.exports.isRequired = function(props, propName, componentName) {
      if (!props[propName]) {
        throw new Error("Prop " + propName + " passed to " + componentName + " is required");
      }
      return module.exports(props, propName, componentName);
    };
    module.exports.supportingArrays = PropTypes2.oneOfType([
      PropTypes2.arrayOf(module.exports),
      module.exports
    ]);
  }
});

// node_modules/react-split-pane/dist/index.esm.js
var import_react = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());
var import_react_style_proptype = __toESM(require_src());

// node_modules/react-lifecycles-compat/react-lifecycles-compat.es.js
function componentWillMount() {
  var state = this.constructor.getDerivedStateFromProps(this.props, this.state);
  if (state !== null && state !== void 0) {
    this.setState(state);
  }
}
function componentWillReceiveProps(nextProps) {
  function updater(prevState) {
    var state = this.constructor.getDerivedStateFromProps(nextProps, prevState);
    return state !== null && state !== void 0 ? state : null;
  }
  this.setState(updater.bind(this));
}
function componentWillUpdate(nextProps, nextState) {
  try {
    var prevProps = this.props;
    var prevState = this.state;
    this.props = nextProps;
    this.state = nextState;
    this.__reactInternalSnapshotFlag = true;
    this.__reactInternalSnapshot = this.getSnapshotBeforeUpdate(
      prevProps,
      prevState
    );
  } finally {
    this.props = prevProps;
    this.state = prevState;
  }
}
componentWillMount.__suppressDeprecationWarning = true;
componentWillReceiveProps.__suppressDeprecationWarning = true;
componentWillUpdate.__suppressDeprecationWarning = true;
function polyfill(Component) {
  var prototype = Component.prototype;
  if (!prototype || !prototype.isReactComponent) {
    throw new Error("Can only polyfill class components");
  }
  if (typeof Component.getDerivedStateFromProps !== "function" && typeof prototype.getSnapshotBeforeUpdate !== "function") {
    return Component;
  }
  var foundWillMountName = null;
  var foundWillReceivePropsName = null;
  var foundWillUpdateName = null;
  if (typeof prototype.componentWillMount === "function") {
    foundWillMountName = "componentWillMount";
  } else if (typeof prototype.UNSAFE_componentWillMount === "function") {
    foundWillMountName = "UNSAFE_componentWillMount";
  }
  if (typeof prototype.componentWillReceiveProps === "function") {
    foundWillReceivePropsName = "componentWillReceiveProps";
  } else if (typeof prototype.UNSAFE_componentWillReceiveProps === "function") {
    foundWillReceivePropsName = "UNSAFE_componentWillReceiveProps";
  }
  if (typeof prototype.componentWillUpdate === "function") {
    foundWillUpdateName = "componentWillUpdate";
  } else if (typeof prototype.UNSAFE_componentWillUpdate === "function") {
    foundWillUpdateName = "UNSAFE_componentWillUpdate";
  }
  if (foundWillMountName !== null || foundWillReceivePropsName !== null || foundWillUpdateName !== null) {
    var componentName = Component.displayName || Component.name;
    var newApiName = typeof Component.getDerivedStateFromProps === "function" ? "getDerivedStateFromProps()" : "getSnapshotBeforeUpdate()";
    throw Error(
      "Unsafe legacy lifecycles will not be called for components using new component APIs.\n\n" + componentName + " uses " + newApiName + " but also contains the following legacy lifecycles:" + (foundWillMountName !== null ? "\n  " + foundWillMountName : "") + (foundWillReceivePropsName !== null ? "\n  " + foundWillReceivePropsName : "") + (foundWillUpdateName !== null ? "\n  " + foundWillUpdateName : "") + "\n\nThe above lifecycles should be removed. Learn more about this warning here:\nhttps://fb.me/react-async-component-lifecycle-hooks"
    );
  }
  if (typeof Component.getDerivedStateFromProps === "function") {
    prototype.componentWillMount = componentWillMount;
    prototype.componentWillReceiveProps = componentWillReceiveProps;
  }
  if (typeof prototype.getSnapshotBeforeUpdate === "function") {
    if (typeof prototype.componentDidUpdate !== "function") {
      throw new Error(
        "Cannot polyfill getSnapshotBeforeUpdate() for components that do not define componentDidUpdate() on the prototype"
      );
    }
    prototype.componentWillUpdate = componentWillUpdate;
    var componentDidUpdate = prototype.componentDidUpdate;
    prototype.componentDidUpdate = function componentDidUpdatePolyfill(prevProps, prevState, maybeSnapshot) {
      var snapshot = this.__reactInternalSnapshotFlag ? this.__reactInternalSnapshot : maybeSnapshot;
      componentDidUpdate.call(this, prevProps, prevState, snapshot);
    };
  }
  return Component;
}

// node_modules/react-split-pane/dist/index.esm.js
function _classCallCheck(instance, Constructor) {
  if (!(instance instanceof Constructor)) {
    throw new TypeError("Cannot call a class as a function");
  }
}
function _defineProperties(target, props) {
  for (var i = 0; i < props.length; i++) {
    var descriptor = props[i];
    descriptor.enumerable = descriptor.enumerable || false;
    descriptor.configurable = true;
    if ("value" in descriptor) descriptor.writable = true;
    Object.defineProperty(target, descriptor.key, descriptor);
  }
}
function _createClass(Constructor, protoProps, staticProps) {
  if (protoProps) _defineProperties(Constructor.prototype, protoProps);
  if (staticProps) _defineProperties(Constructor, staticProps);
  return Constructor;
}
function _defineProperty(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
}
function ownKeys(object, enumerableOnly) {
  var keys = Object.keys(object);
  if (Object.getOwnPropertySymbols) {
    var symbols = Object.getOwnPropertySymbols(object);
    if (enumerableOnly) symbols = symbols.filter(function(sym) {
      return Object.getOwnPropertyDescriptor(object, sym).enumerable;
    });
    keys.push.apply(keys, symbols);
  }
  return keys;
}
function _objectSpread2(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i] != null ? arguments[i] : {};
    if (i % 2) {
      ownKeys(Object(source), true).forEach(function(key) {
        _defineProperty(target, key, source[key]);
      });
    } else if (Object.getOwnPropertyDescriptors) {
      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
      ownKeys(Object(source)).forEach(function(key) {
        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
      });
    }
  }
  return target;
}
function _inherits(subClass, superClass) {
  if (typeof superClass !== "function" && superClass !== null) {
    throw new TypeError("Super expression must either be null or a function");
  }
  subClass.prototype = Object.create(superClass && superClass.prototype, {
    constructor: {
      value: subClass,
      writable: true,
      configurable: true
    }
  });
  if (superClass) _setPrototypeOf(subClass, superClass);
}
function _getPrototypeOf(o) {
  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf2(o2) {
    return o2.__proto__ || Object.getPrototypeOf(o2);
  };
  return _getPrototypeOf(o);
}
function _setPrototypeOf(o, p) {
  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf2(o2, p2) {
    o2.__proto__ = p2;
    return o2;
  };
  return _setPrototypeOf(o, p);
}
function _isNativeReflectConstruct() {
  if (typeof Reflect === "undefined" || !Reflect.construct) return false;
  if (Reflect.construct.sham) return false;
  if (typeof Proxy === "function") return true;
  try {
    Date.prototype.toString.call(Reflect.construct(Date, [], function() {
    }));
    return true;
  } catch (e) {
    return false;
  }
}
function _assertThisInitialized(self) {
  if (self === void 0) {
    throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
  }
  return self;
}
function _possibleConstructorReturn(self, call) {
  if (call && (typeof call === "object" || typeof call === "function")) {
    return call;
  }
  return _assertThisInitialized(self);
}
function _createSuper(Derived) {
  return function() {
    var Super = _getPrototypeOf(Derived), result;
    if (_isNativeReflectConstruct()) {
      var NewTarget = _getPrototypeOf(this).constructor;
      result = Reflect.construct(Super, arguments, NewTarget);
    } else {
      result = Super.apply(this, arguments);
    }
    return _possibleConstructorReturn(this, result);
  };
}
var Pane = function(_React$PureComponent) {
  _inherits(Pane2, _React$PureComponent);
  var _super = _createSuper(Pane2);
  function Pane2() {
    _classCallCheck(this, Pane2);
    return _super.apply(this, arguments);
  }
  _createClass(Pane2, [{
    key: "render",
    value: function render() {
      var _this$props = this.props, children = _this$props.children, className = _this$props.className, split = _this$props.split, styleProps = _this$props.style, size = _this$props.size, eleRef = _this$props.eleRef;
      var classes = ["Pane", split, className];
      var style = {
        flex: 1,
        position: "relative",
        outline: "none"
      };
      if (size !== void 0) {
        if (split === "vertical") {
          style.width = size;
        } else {
          style.height = size;
          style.display = "flex";
        }
        style.flex = "none";
      }
      style = Object.assign({}, style, styleProps || {});
      return import_react.default.createElement("div", {
        ref: eleRef,
        className: classes.join(" "),
        style
      }, children);
    }
  }]);
  return Pane2;
}(import_react.default.PureComponent);
Pane.propTypes = {
  className: import_prop_types.default.string.isRequired,
  children: import_prop_types.default.node.isRequired,
  size: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.number]),
  split: import_prop_types.default.oneOf(["vertical", "horizontal"]),
  style: import_react_style_proptype.default,
  eleRef: import_prop_types.default.func
};
Pane.defaultProps = {};
var RESIZER_DEFAULT_CLASSNAME = "Resizer";
var Resizer = function(_React$Component) {
  _inherits(Resizer2, _React$Component);
  var _super = _createSuper(Resizer2);
  function Resizer2() {
    _classCallCheck(this, Resizer2);
    return _super.apply(this, arguments);
  }
  _createClass(Resizer2, [{
    key: "render",
    value: function render() {
      var _this$props = this.props, className = _this$props.className, _onClick = _this$props.onClick, _onDoubleClick = _this$props.onDoubleClick, _onMouseDown = _this$props.onMouseDown, _onTouchEnd = _this$props.onTouchEnd, _onTouchStart = _this$props.onTouchStart, resizerClassName = _this$props.resizerClassName, split = _this$props.split, style = _this$props.style;
      var classes = [resizerClassName, split, className];
      return import_react.default.createElement("span", {
        role: "presentation",
        className: classes.join(" "),
        style,
        onMouseDown: function onMouseDown(event) {
          return _onMouseDown(event);
        },
        onTouchStart: function onTouchStart(event) {
          event.preventDefault();
          _onTouchStart(event);
        },
        onTouchEnd: function onTouchEnd(event) {
          event.preventDefault();
          _onTouchEnd(event);
        },
        onClick: function onClick(event) {
          if (_onClick) {
            event.preventDefault();
            _onClick(event);
          }
        },
        onDoubleClick: function onDoubleClick(event) {
          if (_onDoubleClick) {
            event.preventDefault();
            _onDoubleClick(event);
          }
        }
      });
    }
  }]);
  return Resizer2;
}(import_react.default.Component);
Resizer.propTypes = {
  className: import_prop_types.default.string.isRequired,
  onClick: import_prop_types.default.func,
  onDoubleClick: import_prop_types.default.func,
  onMouseDown: import_prop_types.default.func.isRequired,
  onTouchStart: import_prop_types.default.func.isRequired,
  onTouchEnd: import_prop_types.default.func.isRequired,
  split: import_prop_types.default.oneOf(["vertical", "horizontal"]),
  style: import_react_style_proptype.default,
  resizerClassName: import_prop_types.default.string.isRequired
};
Resizer.defaultProps = {
  resizerClassName: RESIZER_DEFAULT_CLASSNAME
};
function unFocus(document2, window2) {
  if (document2.selection) {
    document2.selection.empty();
  } else {
    try {
      window2.getSelection().removeAllRanges();
    } catch (e) {
    }
  }
}
function getDefaultSize(defaultSize, minSize, maxSize, draggedSize) {
  if (typeof draggedSize === "number") {
    var min = typeof minSize === "number" ? minSize : 0;
    var max = typeof maxSize === "number" && maxSize >= 0 ? maxSize : Infinity;
    return Math.max(min, Math.min(max, draggedSize));
  }
  if (defaultSize !== void 0) {
    return defaultSize;
  }
  return minSize;
}
function removeNullChildren(children) {
  return import_react.default.Children.toArray(children).filter(function(c) {
    return c;
  });
}
var SplitPane = function(_React$Component) {
  _inherits(SplitPane2, _React$Component);
  var _super = _createSuper(SplitPane2);
  function SplitPane2(props) {
    var _this;
    _classCallCheck(this, SplitPane2);
    _this = _super.call(this, props);
    _this.onMouseDown = _this.onMouseDown.bind(_assertThisInitialized(_this));
    _this.onTouchStart = _this.onTouchStart.bind(_assertThisInitialized(_this));
    _this.onMouseMove = _this.onMouseMove.bind(_assertThisInitialized(_this));
    _this.onTouchMove = _this.onTouchMove.bind(_assertThisInitialized(_this));
    _this.onMouseUp = _this.onMouseUp.bind(_assertThisInitialized(_this));
    var size = props.size, defaultSize = props.defaultSize, minSize = props.minSize, maxSize = props.maxSize, primary = props.primary;
    var initialSize = size !== void 0 ? size : getDefaultSize(defaultSize, minSize, maxSize, null);
    _this.state = {
      active: false,
      resized: false,
      pane1Size: primary === "first" ? initialSize : void 0,
      pane2Size: primary === "second" ? initialSize : void 0,
      // these are props that are needed in static functions. ie: gDSFP
      instanceProps: {
        size
      }
    };
    return _this;
  }
  _createClass(SplitPane2, [{
    key: "componentDidMount",
    value: function componentDidMount() {
      document.addEventListener("mouseup", this.onMouseUp);
      document.addEventListener("mousemove", this.onMouseMove);
      document.addEventListener("touchmove", this.onTouchMove);
      this.setState(SplitPane2.getSizeUpdate(this.props, this.state));
    }
  }, {
    key: "componentWillUnmount",
    value: function componentWillUnmount() {
      document.removeEventListener("mouseup", this.onMouseUp);
      document.removeEventListener("mousemove", this.onMouseMove);
      document.removeEventListener("touchmove", this.onTouchMove);
    }
  }, {
    key: "onMouseDown",
    value: function onMouseDown(event) {
      var eventWithTouches = Object.assign({}, event, {
        touches: [{
          clientX: event.clientX,
          clientY: event.clientY
        }]
      });
      this.onTouchStart(eventWithTouches);
    }
  }, {
    key: "onTouchStart",
    value: function onTouchStart(event) {
      var _this$props = this.props, allowResize = _this$props.allowResize, onDragStarted = _this$props.onDragStarted, split = _this$props.split;
      if (allowResize) {
        unFocus(document, window);
        var position = split === "vertical" ? event.touches[0].clientX : event.touches[0].clientY;
        if (typeof onDragStarted === "function") {
          onDragStarted();
        }
        this.setState({
          active: true,
          position
        });
      }
    }
  }, {
    key: "onMouseMove",
    value: function onMouseMove(event) {
      var eventWithTouches = Object.assign({}, event, {
        touches: [{
          clientX: event.clientX,
          clientY: event.clientY
        }]
      });
      this.onTouchMove(eventWithTouches);
    }
  }, {
    key: "onTouchMove",
    value: function onTouchMove(event) {
      var _this$props2 = this.props, allowResize = _this$props2.allowResize, maxSize = _this$props2.maxSize, minSize = _this$props2.minSize, onChange = _this$props2.onChange, split = _this$props2.split, step = _this$props2.step;
      var _this$state = this.state, active = _this$state.active, position = _this$state.position;
      if (allowResize && active) {
        unFocus(document, window);
        var isPrimaryFirst = this.props.primary === "first";
        var ref = isPrimaryFirst ? this.pane1 : this.pane2;
        var ref2 = isPrimaryFirst ? this.pane2 : this.pane1;
        if (ref) {
          var node = ref;
          var node2 = ref2;
          if (node.getBoundingClientRect) {
            var width = node.getBoundingClientRect().width;
            var height = node.getBoundingClientRect().height;
            var current = split === "vertical" ? event.touches[0].clientX : event.touches[0].clientY;
            var size = split === "vertical" ? width : height;
            var positionDelta = position - current;
            if (step) {
              if (Math.abs(positionDelta) < step) {
                return;
              }
              positionDelta = ~~(positionDelta / step) * step;
            }
            var sizeDelta = isPrimaryFirst ? positionDelta : -positionDelta;
            var pane1Order = parseInt(window.getComputedStyle(node).order);
            var pane2Order = parseInt(window.getComputedStyle(node2).order);
            if (pane1Order > pane2Order) {
              sizeDelta = -sizeDelta;
            }
            var newMaxSize = maxSize;
            if (maxSize !== void 0 && maxSize <= 0) {
              var splitPane = this.splitPane;
              if (split === "vertical") {
                newMaxSize = splitPane.getBoundingClientRect().width + maxSize;
              } else {
                newMaxSize = splitPane.getBoundingClientRect().height + maxSize;
              }
            }
            var newSize = size - sizeDelta;
            var newPosition = position - positionDelta;
            if (newSize < minSize) {
              newSize = minSize;
            } else if (maxSize !== void 0 && newSize > newMaxSize) {
              newSize = newMaxSize;
            } else {
              this.setState({
                position: newPosition,
                resized: true
              });
            }
            if (onChange) onChange(newSize);
            this.setState(_defineProperty({
              draggedSize: newSize
            }, isPrimaryFirst ? "pane1Size" : "pane2Size", newSize));
          }
        }
      }
    }
  }, {
    key: "onMouseUp",
    value: function onMouseUp() {
      var _this$props3 = this.props, allowResize = _this$props3.allowResize, onDragFinished = _this$props3.onDragFinished;
      var _this$state2 = this.state, active = _this$state2.active, draggedSize = _this$state2.draggedSize;
      if (allowResize && active) {
        if (typeof onDragFinished === "function") {
          onDragFinished(draggedSize);
        }
        this.setState({
          active: false
        });
      }
    }
    // we have to check values since gDSFP is called on every render and more in StrictMode
  }, {
    key: "render",
    value: function render() {
      var _this2 = this;
      var _this$props4 = this.props, allowResize = _this$props4.allowResize, children = _this$props4.children, className = _this$props4.className, onResizerClick = _this$props4.onResizerClick, onResizerDoubleClick = _this$props4.onResizerDoubleClick, paneClassName = _this$props4.paneClassName, pane1ClassName = _this$props4.pane1ClassName, pane2ClassName = _this$props4.pane2ClassName, paneStyle = _this$props4.paneStyle, pane1StyleProps = _this$props4.pane1Style, pane2StyleProps = _this$props4.pane2Style, resizerClassName = _this$props4.resizerClassName, resizerStyle = _this$props4.resizerStyle, split = _this$props4.split, styleProps = _this$props4.style;
      var _this$state3 = this.state, pane1Size = _this$state3.pane1Size, pane2Size = _this$state3.pane2Size;
      var disabledClass = allowResize ? "" : "disabled";
      var resizerClassNamesIncludingDefault = resizerClassName ? "".concat(resizerClassName, " ").concat(RESIZER_DEFAULT_CLASSNAME) : resizerClassName;
      var notNullChildren = removeNullChildren(children);
      var style = _objectSpread2({
        display: "flex",
        flex: 1,
        height: "100%",
        position: "absolute",
        outline: "none",
        overflow: "hidden",
        MozUserSelect: "text",
        WebkitUserSelect: "text",
        msUserSelect: "text",
        userSelect: "text"
      }, styleProps);
      if (split === "vertical") {
        Object.assign(style, {
          flexDirection: "row",
          left: 0,
          right: 0
        });
      } else {
        Object.assign(style, {
          bottom: 0,
          flexDirection: "column",
          minHeight: "100%",
          top: 0,
          width: "100%"
        });
      }
      var classes = ["SplitPane", className, split, disabledClass];
      var pane1Style = _objectSpread2({}, paneStyle, {}, pane1StyleProps);
      var pane2Style = _objectSpread2({}, paneStyle, {}, pane2StyleProps);
      var pane1Classes = ["Pane1", paneClassName, pane1ClassName].join(" ");
      var pane2Classes = ["Pane2", paneClassName, pane2ClassName].join(" ");
      return import_react.default.createElement("div", {
        className: classes.join(" "),
        ref: function ref(node) {
          _this2.splitPane = node;
        },
        style
      }, import_react.default.createElement(Pane, {
        className: pane1Classes,
        key: "pane1",
        eleRef: function eleRef(node) {
          _this2.pane1 = node;
        },
        size: pane1Size,
        split,
        style: pane1Style
      }, notNullChildren[0]), import_react.default.createElement(Resizer, {
        className: disabledClass,
        onClick: onResizerClick,
        onDoubleClick: onResizerDoubleClick,
        onMouseDown: this.onMouseDown,
        onTouchStart: this.onTouchStart,
        onTouchEnd: this.onMouseUp,
        key: "resizer",
        resizerClassName: resizerClassNamesIncludingDefault,
        split,
        style: resizerStyle || {}
      }), import_react.default.createElement(Pane, {
        className: pane2Classes,
        key: "pane2",
        eleRef: function eleRef(node) {
          _this2.pane2 = node;
        },
        size: pane2Size,
        split,
        style: pane2Style
      }, notNullChildren[1]));
    }
  }], [{
    key: "getDerivedStateFromProps",
    value: function getDerivedStateFromProps(nextProps, prevState) {
      return SplitPane2.getSizeUpdate(nextProps, prevState);
    }
  }, {
    key: "getSizeUpdate",
    value: function getSizeUpdate(props, state) {
      var newState = {};
      var instanceProps = state.instanceProps;
      if (instanceProps.size === props.size && props.size !== void 0) {
        return {};
      }
      var newSize = props.size !== void 0 ? props.size : getDefaultSize(props.defaultSize, props.minSize, props.maxSize, state.draggedSize);
      if (props.size !== void 0) {
        newState.draggedSize = newSize;
      }
      var isPanel1Primary = props.primary === "first";
      newState[isPanel1Primary ? "pane1Size" : "pane2Size"] = newSize;
      newState[isPanel1Primary ? "pane2Size" : "pane1Size"] = void 0;
      newState.instanceProps = {
        size: props.size
      };
      return newState;
    }
  }]);
  return SplitPane2;
}(import_react.default.Component);
SplitPane.propTypes = {
  allowResize: import_prop_types.default.bool,
  children: import_prop_types.default.arrayOf(import_prop_types.default.node).isRequired,
  className: import_prop_types.default.string,
  primary: import_prop_types.default.oneOf(["first", "second"]),
  minSize: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.number]),
  maxSize: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.number]),
  // eslint-disable-next-line react/no-unused-prop-types
  defaultSize: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.number]),
  size: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.number]),
  split: import_prop_types.default.oneOf(["vertical", "horizontal"]),
  onDragStarted: import_prop_types.default.func,
  onDragFinished: import_prop_types.default.func,
  onChange: import_prop_types.default.func,
  onResizerClick: import_prop_types.default.func,
  onResizerDoubleClick: import_prop_types.default.func,
  style: import_react_style_proptype.default,
  resizerStyle: import_react_style_proptype.default,
  paneClassName: import_prop_types.default.string,
  pane1ClassName: import_prop_types.default.string,
  pane2ClassName: import_prop_types.default.string,
  paneStyle: import_react_style_proptype.default,
  pane1Style: import_react_style_proptype.default,
  pane2Style: import_react_style_proptype.default,
  resizerClassName: import_prop_types.default.string,
  step: import_prop_types.default.number
};
SplitPane.defaultProps = {
  allowResize: true,
  minSize: 50,
  primary: "first",
  split: "vertical",
  paneClassName: "",
  pane1ClassName: "",
  pane2ClassName: ""
};
polyfill(SplitPane);
var index_esm_default = SplitPane;
export {
  Pane,
  index_esm_default as default
};
/*! Bundled license information:

react-is/cjs/react-is.development.js:
  (** @license React v16.13.1
   * react-is.development.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

object-assign/index.js:
  (*
  object-assign
  (c) Sindre Sorhus
  @license MIT
  *)
*/
//# sourceMappingURL=react-split-pane.js.map
