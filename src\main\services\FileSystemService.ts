import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';
import * as chokidar from 'chokidar';
import { EventEmitter } from 'events';

// Promisify fs methods for async/await usage
const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);
const access = promisify(fs.access);
const mkdir = promisify(fs.mkdir);
const rename = promisify(fs.rename);
const unlink = promisify(fs.unlink);
const rmdir = promisify(fs.rm);

export interface FileInfo {
  name: string;
  path: string;
  isDirectory: boolean;
  size: number;
  modified: Date;
  created: Date;
  extension?: string;
  children?: FileInfo[];
}

export interface WatcherOptions {
  ignored?: string | RegExp | (string | RegExp)[];
  persistent?: boolean;
  ignoreInitial?: boolean;
  followSymlinks?: boolean;
  cwd?: string;
  disableGlobbing?: boolean;
  usePolling?: boolean;
  interval?: number;
  binaryInterval?: number;
  alwaysStat?: boolean;
  depth?: number;
  awaitWriteFinish?: boolean | {
    stabilityThreshold?: number;
    pollInterval?: number;
  };
}

export class FileSystemService extends EventEmitter {
  private watchers: Map<string, chokidar.FSWatcher> = new Map();

  constructor() {
    super();
  }

  // Basic file operations
  public async readFile(filePath: string, encoding: BufferEncoding = 'utf8'): Promise<string> {
    try {
      return await readFile(filePath, encoding);
    } catch (error) {
      throw new Error(`Failed to read file ${filePath}: ${error.message}`);
    }
  }

  public async writeFile(filePath: string, content: string, encoding: BufferEncoding = 'utf8'): Promise<void> {
    try {
      // Ensure directory exists
      const dir = path.dirname(filePath);
      await this.ensureDirectory(dir);
      
      await writeFile(filePath, content, encoding);
      this.emit('fileChanged', { type: 'modified', path: filePath });
    } catch (error) {
      throw new Error(`Failed to write file ${filePath}: ${error.message}`);
    }
  }

  public async readDirectory(dirPath: string, recursive: boolean = false): Promise<FileInfo[]> {
    try {
      const items = await readdir(dirPath, { withFileTypes: true });
      const fileInfos: FileInfo[] = [];

      for (const item of items) {
        const itemPath = path.join(dirPath, item.name);
        const stats = await stat(itemPath);
        
        const fileInfo: FileInfo = {
          name: item.name,
          path: itemPath,
          isDirectory: item.isDirectory(),
          size: stats.size,
          modified: stats.mtime,
          created: stats.birthtime,
          extension: item.isFile() ? path.extname(item.name) : undefined
        };

        if (recursive && item.isDirectory()) {
          try {
            fileInfo.children = await this.readDirectory(itemPath, true);
          } catch (error) {
            // Skip directories that can't be read (permissions, etc.)
            console.warn(`Cannot read directory ${itemPath}:`, error.message);
            fileInfo.children = [];
          }
        }

        fileInfos.push(fileInfo);
      }

      return fileInfos.sort((a, b) => {
        // Directories first, then files
        if (a.isDirectory && !b.isDirectory) return -1;
        if (!a.isDirectory && b.isDirectory) return 1;
        return a.name.localeCompare(b.name);
      });
    } catch (error) {
      throw new Error(`Failed to read directory ${dirPath}: ${error.message}`);
    }
  }

  public async getFileInfo(filePath: string): Promise<FileInfo> {
    try {
      const stats = await stat(filePath);
      const name = path.basename(filePath);
      
      return {
        name,
        path: filePath,
        isDirectory: stats.isDirectory(),
        size: stats.size,
        modified: stats.mtime,
        created: stats.birthtime,
        extension: stats.isFile() ? path.extname(name) : undefined
      };
    } catch (error) {
      throw new Error(`Failed to get file info for ${filePath}: ${error.message}`);
    }
  }

  public async exists(filePath: string): Promise<boolean> {
    try {
      await access(filePath, fs.constants.F_OK);
      return true;
    } catch {
      return false;
    }
  }

  public async ensureDirectory(dirPath: string): Promise<void> {
    try {
      await mkdir(dirPath, { recursive: true });
    } catch (error) {
      if (error.code !== 'EEXIST') {
        throw new Error(`Failed to create directory ${dirPath}: ${error.message}`);
      }
    }
  }

  public async createFile(filePath: string, content: string = ''): Promise<void> {
    try {
      if (await this.exists(filePath)) {
        throw new Error(`File ${filePath} already exists`);
      }
      
      await this.writeFile(filePath, content);
      this.emit('fileChanged', { type: 'created', path: filePath });
    } catch (error) {
      throw new Error(`Failed to create file ${filePath}: ${error.message}`);
    }
  }

  public async createDirectory(dirPath: string): Promise<void> {
    try {
      if (await this.exists(dirPath)) {
        throw new Error(`Directory ${dirPath} already exists`);
      }
      
      await this.ensureDirectory(dirPath);
      this.emit('fileChanged', { type: 'created', path: dirPath });
    } catch (error) {
      throw new Error(`Failed to create directory ${dirPath}: ${error.message}`);
    }
  }

  public async renameFile(oldPath: string, newPath: string): Promise<void> {
    try {
      await rename(oldPath, newPath);
      this.emit('fileChanged', { type: 'renamed', path: newPath, oldPath });
    } catch (error) {
      throw new Error(`Failed to rename ${oldPath} to ${newPath}: ${error.message}`);
    }
  }

  public async deleteFile(filePath: string): Promise<void> {
    try {
      await unlink(filePath);
      this.emit('fileChanged', { type: 'deleted', path: filePath });
    } catch (error) {
      throw new Error(`Failed to delete file ${filePath}: ${error.message}`);
    }
  }

  public async deleteDirectory(dirPath: string, recursive: boolean = false): Promise<void> {
    try {
      await rmdir(dirPath, { recursive, force: true });
      this.emit('fileChanged', { type: 'deleted', path: dirPath });
    } catch (error) {
      throw new Error(`Failed to delete directory ${dirPath}: ${error.message}`);
    }
  }

  public async copyFile(sourcePath: string, destPath: string): Promise<void> {
    try {
      const content = await this.readFile(sourcePath);
      await this.writeFile(destPath, content);
      this.emit('fileChanged', { type: 'created', path: destPath });
    } catch (error) {
      throw new Error(`Failed to copy ${sourcePath} to ${destPath}: ${error.message}`);
    }
  }

  // File watching
  public watchPath(watchPath: string, options: WatcherOptions = {}): string {
    const watcherId = `${watchPath}_${Date.now()}`;
    
    const defaultOptions: WatcherOptions = {
      ignored: [
        /(^|[\/\\])\../, // ignore dotfiles
        /node_modules/,
        /\.git/,
        /dist/,
        /build/,
        /coverage/
      ],
      persistent: true,
      ignoreInitial: true,
      followSymlinks: false,
      depth: 10,
      awaitWriteFinish: {
        stabilityThreshold: 100,
        pollInterval: 100
      }
    };

    const finalOptions = { ...defaultOptions, ...options };
    
    const watcher = chokidar.watch(watchPath, finalOptions);
    
    watcher
      .on('add', (filePath) => {
        this.emit('fileChanged', { type: 'created', path: filePath });
      })
      .on('change', (filePath) => {
        this.emit('fileChanged', { type: 'modified', path: filePath });
      })
      .on('unlink', (filePath) => {
        this.emit('fileChanged', { type: 'deleted', path: filePath });
      })
      .on('addDir', (dirPath) => {
        this.emit('fileChanged', { type: 'created', path: dirPath });
      })
      .on('unlinkDir', (dirPath) => {
        this.emit('fileChanged', { type: 'deleted', path: dirPath });
      })
      .on('error', (error) => {
        this.emit('watchError', { watcherId, error });
      });

    this.watchers.set(watcherId, watcher);
    return watcherId;
  }

  public unwatchPath(watcherId: string): void {
    const watcher = this.watchers.get(watcherId);
    if (watcher) {
      watcher.close();
      this.watchers.delete(watcherId);
    }
  }

  public unwatchAll(): void {
    for (const [watcherId, watcher] of this.watchers) {
      watcher.close();
    }
    this.watchers.clear();
  }

  // Utility methods
  public getFileExtension(filePath: string): string {
    return path.extname(filePath).toLowerCase();
  }

  public getFileName(filePath: string, includeExtension: boolean = true): string {
    return includeExtension ? path.basename(filePath) : path.basename(filePath, path.extname(filePath));
  }

  public getDirectoryName(filePath: string): string {
    return path.dirname(filePath);
  }

  public joinPath(...paths: string[]): string {
    return path.join(...paths);
  }

  public resolvePath(...paths: string[]): string {
    return path.resolve(...paths);
  }

  public isAbsolutePath(filePath: string): boolean {
    return path.isAbsolute(filePath);
  }

  public relativePath(from: string, to: string): string {
    return path.relative(from, to);
  }

  // Search functionality
  public async searchFiles(searchPath: string, pattern: string | RegExp, options: {
    includeContent?: boolean;
    maxResults?: number;
    fileExtensions?: string[];
  } = {}): Promise<Array<{ path: string; matches?: string[] }>> {
    const results: Array<{ path: string; matches?: string[] }> = [];
    const { includeContent = false, maxResults = 100, fileExtensions } = options;
    
    const searchRegex = typeof pattern === 'string' ? new RegExp(pattern, 'gi') : pattern;
    
    const searchInDirectory = async (dirPath: string) => {
      if (results.length >= maxResults) return;
      
      try {
        const items = await this.readDirectory(dirPath);
        
        for (const item of items) {
          if (results.length >= maxResults) break;
          
          if (item.isDirectory) {
            await searchInDirectory(item.path);
          } else {
            // Check file extension filter
            if (fileExtensions && fileExtensions.length > 0) {
              const ext = this.getFileExtension(item.path);
              if (!fileExtensions.includes(ext)) continue;
            }
            
            // Check filename match
            if (searchRegex.test(item.name)) {
              const result: { path: string; matches?: string[] } = { path: item.path };
              
              if (includeContent) {
                try {
                  const content = await this.readFile(item.path);
                  const matches = content.match(searchRegex);
                  if (matches) {
                    result.matches = matches;
                  }
                } catch {
                  // Skip files that can't be read
                }
              }
              
              results.push(result);
            }
          }
        }
      } catch {
        // Skip directories that can't be read
      }
    };
    
    await searchInDirectory(searchPath);
    return results;
  }

  public destroy(): void {
    this.unwatchAll();
    this.removeAllListeners();
  }
}
