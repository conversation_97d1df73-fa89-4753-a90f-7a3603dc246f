interface FileInfo {
  name: string;
  isDirectory: boolean;
  path: string;
}

interface StatInfo {
  size: number;
  isFile: boolean;
  isDirectory: boolean;
  created: Date;
  modified: Date;
}

interface OsInfo {
  platform: string;
  release: string;
  arch: string;
  cpus: {
    model: string;
    speed: number;
    times: {
      user: number;
      nice: number;
      sys: number;
      idle: number;
      irq: number;
    };
  }[];
  totalMemory: number;
  freeMemory: number;
}

interface Project {
  id: number;
  name: string;
  path: string;
  lastOpened: string;
  settings: string;
  created: string;
}

interface UserSettings {
  id: number;
  key: string;
  value: string;
  category: string;
}

interface Extension {
  id: number;
  name: string;
  version: string;
  type: 'vsix' | 'koix';
  enabled: boolean;
  path: string;
  manifest: string;
  installed: string;
}

interface Theme {
  id: number;
  name: string;
  type: 'builtin' | 'custom';
  path: string;
  manifest: string;
  active: boolean;
  installed: string;
}

interface EnhancedFileInfo extends FileInfo {
  size: number;
  modified: Date;
  created: Date;
  extension?: string;
  children?: EnhancedFileInfo[];
}

interface TerminalProcess {
  id: number;
  shell: string;
  cwd: string;
  cols: number;
  rows: number;
  created: Date;
}

interface TerminalOptions {
  shell?: string;
  cwd?: string;
  env?: Record<string, string>;
  cols?: number;
  rows?: number;
}

interface ElectronAPI {
  appInfo: {
    getVersion: () => Promise<string>;
    getPlatform: () => string;
    getOsInfo: () => OsInfo;
  };
  fileSystem: {
    // Enhanced file system operations
    readFile: (path: string) => Promise<string>;
    writeFile: (path: string, content: string) => Promise<void>;
    readDirectory: (path: string, recursive?: boolean) => Promise<EnhancedFileInfo[]>;
    createFile: (path: string, content?: string) => Promise<void>;
    createDirectory: (path: string) => Promise<void>;
    renameFile: (oldPath: string, newPath: string) => Promise<void>;
    deleteFile: (path: string) => Promise<void>;
    deleteDirectory: (path: string, recursive?: boolean) => Promise<void>;

    // Legacy methods
    readDir: (path: string) => Promise<FileInfo[]>;
    stat: (path: string) => Promise<StatInfo>;
    exists: (path: string) => Promise<boolean>;
    mkdir: (path: string) => Promise<void>;
    rename: (oldPath: string, newPath: string) => Promise<void>;
    unlink: (path: string) => Promise<void>;
    rmdir: (path: string) => Promise<void>;
    watch: (path: string, callback: (eventType: string, filename: string | null) => void) => () => void;
  };
  database: {
    getRecentProjects: () => Promise<Project[]>;
    getSettings: (category: string) => Promise<UserSettings[]>;
    setSetting: (key: string, value: string, category: string) => Promise<void>;
  };
  project: {
    open: (projectPath: string) => Promise<Project>;
    close: () => Promise<void>;
    create: (projectPath: string, template?: any) => Promise<Project>;
    getCurrent: () => Promise<Project | null>;
    analyze: (projectPath: string) => Promise<{
      type: string;
      framework?: string;
      language: string;
      hasTests: boolean;
      hasLinting: boolean;
      hasTypeScript: boolean;
      dependencies: string[];
      devDependencies: string[];
    }>;
  };
  dialog: {
    openDirectory: () => Promise<{ canceled: boolean; filePaths: string[] }>;
    openFile: (options?: any) => Promise<{ canceled: boolean; filePaths: string[] }>;
    saveFile: (options?: any) => Promise<{ canceled: boolean; filePath?: string }>;
  };
  terminal: {
    create: (options?: TerminalOptions) => Promise<TerminalProcess>;
    write: (id: number, data: string) => Promise<boolean>;
    resize: (id: number, cols: number, rows: number) => Promise<boolean>;
    kill: (id: number) => Promise<boolean>;
    getAll: () => Promise<TerminalProcess[]>;
    get: (id: number) => Promise<TerminalProcess | null>;
  };
  ipc: {
    send: (channel: string, data?: any) => void;
    on: (channel: string, callback: (...args: any[]) => void) => () => void;
    once: (channel: string, callback: (...args: any[]) => void) => void;
    invoke: (channel: string, ...args: any[]) => Promise<any>;
  };
  path: {
    join: (...args: string[]) => string;
    dirname: (path: string) => string;
    basename: (path: string, ext?: string) => string;
    extname: (path: string) => string;
    resolve: (...args: string[]) => string;
    isAbsolute: (path: string) => boolean;
  };
}

declare interface Window {
  electron: ElectronAPI;
}
