const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');
const os = require('os');
const path = require('path');
const fs = require('fs');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('electron', {
  // App info
  appInfo: {
    getVersion: () => ipcRenderer.invoke('app-version'),
    getPlatform: () => process.platform,
    getOsInfo: () => ({
      platform: os.platform(),
      release: os.release(),
      arch: os.arch(),
      cpus: os.cpus(),
      totalMemory: os.totalmem(),
      freeMemory: os.freemem()
    })
  },
  
  // File system operations
  fileSystem: {
    readFile: (filePath) => {
      return new Promise((resolve, reject) => {
        fs.readFile(filePath, 'utf8', (err, data) => {
          if (err) {
            reject(err);
            return;
          }
          resolve(data);
        });
      });
    },
    writeFile: (filePath, content) => {
      return new Promise((resolve, reject) => {
        fs.writeFile(filePath, content, 'utf8', (err) => {
          if (err) {
            reject(err);
            return;
          }
          resolve();
        });
      });
    },
    readDir: (dirPath) => {
      return new Promise((resolve, reject) => {
        fs.readdir(dirPath, { withFileTypes: true }, (err, files) => {
          if (err) {
            reject(err);
            return;
          }
          
          const fileInfos = files.map(file => ({
            name: file.name,
            isDirectory: file.isDirectory(),
            path: path.join(dirPath, file.name)
          }));
          
          resolve(fileInfos);
        });
      });
    },
    stat: (filePath) => {
      return new Promise((resolve, reject) => {
        fs.stat(filePath, (err, stats) => {
          if (err) {
            reject(err);
            return;
          }
          resolve({
            size: stats.size,
            isFile: stats.isFile(),
            isDirectory: stats.isDirectory(),
            created: stats.birthtime,
            modified: stats.mtime
          });
        });
      });
    },
    exists: (path) => {
      return new Promise((resolve) => {
        fs.access(path, fs.constants.F_OK, (err) => {
          resolve(!err);
        });
      });
    },
    mkdir: (dirPath) => {
      return new Promise((resolve, reject) => {
        fs.mkdir(dirPath, { recursive: true }, (err) => {
          if (err) {
            reject(err);
            return;
          }
          resolve();
        });
      });
    },
    rename: (oldPath, newPath) => {
      return new Promise((resolve, reject) => {
        fs.rename(oldPath, newPath, (err) => {
          if (err) {
            reject(err);
            return;
          }
          resolve();
        });
      });
    },
    unlink: (filePath) => {
      return new Promise((resolve, reject) => {
        fs.unlink(filePath, (err) => {
          if (err) {
            reject(err);
            return;
          }
          resolve();
        });
      });
    },
    rmdir: (dirPath) => {
      return new Promise((resolve, reject) => {
        fs.rm(dirPath, { recursive: true, force: true }, (err) => {
          if (err) {
            reject(err);
            return;
          }
          resolve();
        });
      });
    },
    watch: (path, callback) => {
      const watcher = fs.watch(path, { recursive: true }, (eventType, filename) => {
        callback(eventType, filename);
      });
      
      return () => {
        watcher.close();
      };
    }
  },
  
  // IPC communication
  ipc: {
    send: (channel, data) => {
      ipcRenderer.send(channel, data);
    },
    on: (channel, callback) => {
      // Deliberately strip event as it includes `sender` 
      ipcRenderer.on(channel, (event, ...args) => callback(...args));
      
      // Return a function to remove the listener
      return () => {
        ipcRenderer.removeListener(channel, callback);
      };
    },
    once: (channel, callback) => {
      ipcRenderer.once(channel, (event, ...args) => callback(...args));
    },
    invoke: (channel, ...args) => {
      return ipcRenderer.invoke(channel, ...args);
    }
  },
  
  // Path utilities
  path: {
    join: (...args) => path.join(...args),
    dirname: (p) => path.dirname(p),
    basename: (p, ext) => path.basename(p, ext),
    extname: (p) => path.extname(p),
    resolve: (...args) => path.resolve(...args),
    isAbsolute: (p) => path.isAbsolute(p)
  }
});
