# ⚡ KilatCode IDE

**Modern Desktop IDE with Nusantara Glow Theme**

KilatCode IDE adalah IDE desktop modern yang menggabungkan kekuatan Visual Studio Code dan Onlook IDE dengan sentuhan budaya Nusantara yang unik. <PERSON><PERSON><PERSON> dengan Electron, React, dan <PERSON>Script untuk memberikan pengalaman development yang luar biasa.

## ✨ Fitur Utama

### 🎨 **Nusantara Glow Theme**
- Tema gelap modern dengan aksen neon biru-cyan
- Pola batik halus sebagai background
- Efek glow yang memukau
- Font dan ikonografi tech-modern

### 🔧 **Core Features**
- **Monaco Editor** dengan Tree-sitter syntax highlighting
- **Real Terminal** dengan dukungan multi-tab
- **File Explorer** dengan monitoring real-time
- **SQLite Integration** untuk data lokal
- **Git Integration** dengan visual tools
- **Live Preview** untuk web development
- **Visual Editor** drag-n-drop (seperti Onlook)

### 🤖 **<PERSON><PERSON> Ai Assistant**
- AI assistant lokal dan online
- Code completion cerdas
- Debug otomatis
- Generate unit tests
- Dokumentasi otomatis

### 🔌 **Plugin System**
- Support ekstensi VSCode (.vsix)
- Format lokal KilatCode (.koix)
- Marketplace terintegrasi
- Auto-update plugins

### 🌐 **Online & Offline Support**
- Bekerja sepenuhnya offline
- Sync online untuk marketplace dan preferensi
- Cloud backup untuk settings

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- npm atau yarn
- Git

### Installation

```bash
# Clone repository
git clone https://github.com/kilatcode/kilatcode-ide.git
cd kilatcode-ide

# Install dependencies
npm install

# Start development
npm run dev
```

### Build for Production

```bash
# Build aplikasi
npm run build

# Create distributable
npm run dist

# Platform specific builds
npm run dist:win    # Windows (.exe, .msi)
npm run dist:mac    # macOS (.dmg)
npm run dist:linux  # Linux (.AppImage, .deb, .rpm)
```

## 🏗️ Arsitektur

```
kilatcode-ide/
├── src/
│   ├── main/           # Electron main process
│   └── renderer/       # React frontend
├── assets/             # Icons, themes, splash
├── plugins/            # .koix plugins
├── extensions/         # .vsix support
├── marketplace/        # Plugin marketplace
├── api/                # REST API backend
├── ai/                 # Mbah Ai engine
└── tests/              # Test suites
```

## 🎯 Roadmap

- [x] Project setup & architecture
- [x] Core UI & Nusantara Glow theme
- [ ] Monaco Editor integration
- [ ] File system & project management
- [ ] Terminal integration
- [ ] SQLite database layer
- [ ] Plugin system (.vsix & .koix)
- [ ] LSP integration
- [ ] Git tools
- [ ] Live preview & visual editor
- [ ] Mbah Ai assistant
- [ ] Testing framework
- [ ] Marketplace
- [ ] Distribution & auto-updater

## 🤝 Contributing

Kami menyambut kontribusi dari komunitas! Silakan baca [CONTRIBUTING.md](CONTRIBUTING.md) untuk panduan kontribusi.

### Development Setup

```bash
# Fork dan clone repository
git clone https://github.com/your-username/kilatcode-ide.git

# Install dependencies
npm install

# Start development server
npm run dev

# Run tests
npm test

# Lint code
npm run lint
```

## 📄 License

MIT License - lihat [LICENSE](LICENSE) untuk detail lengkap.

## 🙏 Acknowledgments

- **Visual Studio Code** - Inspirasi untuk fitur editor
- **Onlook IDE** - Inspirasi untuk visual editing
- **Monaco Editor** - Code editor engine
- **Electron** - Desktop app framework
- **React** - UI framework

## 📞 Support

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/kilatcode/kilatcode-ide/issues)
- 💬 Discussions: [GitHub Discussions](https://github.com/kilatcode/kilatcode-ide/discussions)
- 📖 Documentation: [docs.kilatcode.com](https://docs.kilatcode.com)

---

**Made with ⚡ and ❤️ in Indonesia**

*KilatCode IDE - Where Indonesian creativity meets modern technology*
