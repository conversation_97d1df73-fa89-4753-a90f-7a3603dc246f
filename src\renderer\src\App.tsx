import React, { useState, useEffect } from 'react';
import { Routes, Route } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import WelcomeScreen from './pages/WelcomeScreen';
import IDELayout from './components/layout/IDELayout';
import './styles/App.css';

// Type definitions for Electron API
declare global {
  interface Window {
    electron: {
      appInfo: {
        getVersion: () => Promise<string>;
        getPlatform: () => string;
        getOsInfo: () => any;
      };
      fileSystem: {
        // Enhanced file system operations
        readFile: (path: string) => Promise<string>;
        writeFile: (path: string, content: string) => Promise<void>;
        readDirectory: (path: string, recursive?: boolean) => Promise<any[]>;
        createFile: (path: string, content?: string) => Promise<void>;
        createDirectory: (path: string) => Promise<void>;
        renameFile: (oldPath: string, newPath: string) => Promise<void>;
        deleteFile: (path: string) => Promise<void>;
        deleteDirectory: (path: string, recursive?: boolean) => Promise<void>;

        // Legacy methods
        readDir: (path: string) => Promise<any[]>;
        stat: (path: string) => Promise<any>;
        exists: (path: string) => Promise<boolean>;
        mkdir: (path: string) => Promise<void>;
        rename: (oldPath: string, newPath: string) => Promise<void>;
        unlink: (path: string) => Promise<void>;
        rmdir: (path: string) => Promise<void>;
        watch: (path: string, callback: (eventType: string, filename: string) => void) => () => void;
      };
      database: {
        getRecentProjects: () => Promise<any[]>;
        getSettings: (category: string) => Promise<any[]>;
        setSetting: (key: string, value: string, category: string) => Promise<void>;
      };
      project: {
        open: (projectPath: string) => Promise<any>;
        close: () => Promise<void>;
        create: (projectPath: string, template?: any) => Promise<any>;
        getCurrent: () => Promise<any>;
        analyze: (projectPath: string) => Promise<any>;
      };
      dialog: {
        openDirectory: () => Promise<any>;
        openFile: (options?: any) => Promise<any>;
        saveFile: (options?: any) => Promise<any>;
      };
      ipc: {
        send: (channel: string, data?: any) => void;
        on: (channel: string, callback: (...args: any[]) => void) => () => void;
        once: (channel: string, callback: (...args: any[]) => void) => void;
        invoke: (channel: string, ...args: any[]) => Promise<any>;
      };
      path: {
        join: (...args: string[]) => string;
        dirname: (path: string) => string;
        basename: (path: string, ext?: string) => string;
        extname: (path: string) => string;
        resolve: (...args: string[]) => string;
        isAbsolute: (path: string) => boolean;
      };
    };
  }
}

interface AppState {
  hasOpenProject: boolean;
  currentProject: string | null;
  appVersion: string;
  isLoading: boolean;
}

const App: React.FC = () => {
  const [appState, setAppState] = useState<AppState>({
    hasOpenProject: false,
    currentProject: null,
    appVersion: '1.0.0',
    isLoading: true
  });

  useEffect(() => {
    // Initialize app
    const initializeApp = async () => {
      try {
        // Get app version
        const version = await window.electron.appInfo.getVersion();
        setAppState(prev => ({ ...prev, appVersion: version }));

        // Check for existing project or workspace
        // This will be implemented when we add project management
        
        // Simulate loading time for smooth UX
        setTimeout(() => {
          setAppState(prev => ({ ...prev, isLoading: false }));
        }, 1000);
        
      } catch (error) {
        console.error('Failed to initialize app:', error);
        setAppState(prev => ({ ...prev, isLoading: false }));
      }
    };

    initializeApp();

    // Set up IPC listeners for menu actions
    const removeMenuListeners = [
      window.electron.ipc.on('menu-new-file', () => {
        console.log('New file requested');
        // TODO: Implement new file creation
      }),
      
      window.electron.ipc.on('menu-open-file', (filePath: string) => {
        console.log('Open file requested:', filePath);
        // TODO: Implement file opening
      }),
      
      window.electron.ipc.on('menu-open-folder', (folderPath: string) => {
        console.log('Open folder requested:', folderPath);
        setAppState(prev => ({
          ...prev,
          hasOpenProject: true,
          currentProject: folderPath
        }));
      }),
      
      window.electron.ipc.on('menu-save', () => {
        console.log('Save requested');
        // TODO: Implement save functionality
      }),
      
      window.electron.ipc.on('menu-save-as', () => {
        console.log('Save as requested');
        // TODO: Implement save as functionality
      }),
      
      window.electron.ipc.on('menu-command-palette', () => {
        console.log('Command palette requested');
        // TODO: Implement command palette
      }),
      
      window.electron.ipc.on('menu-new-terminal', () => {
        console.log('New terminal requested');
        // TODO: Implement terminal creation
      }),
      
      window.electron.ipc.on('menu-about', () => {
        console.log('About dialog requested');
        // TODO: Implement about dialog
      })
    ];

    // Cleanup listeners on unmount
    return () => {
      removeMenuListeners.forEach(removeListener => removeListener());
    };
  }, []);

  const handleOpenProject = (projectPath: string) => {
    setAppState(prev => ({
      ...prev,
      hasOpenProject: true,
      currentProject: projectPath
    }));
  };

  const handleCloseProject = () => {
    setAppState(prev => ({
      ...prev,
      hasOpenProject: false,
      currentProject: null
    }));
  };

  if (appState.isLoading) {
    return (
      <div className="app-loading">
        <div className="loading-spinner">
          <div className="spinner-ring"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="app">
      <AnimatePresence mode="wait">
        {!appState.hasOpenProject ? (
          <motion.div
            key="welcome"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Routes>
              <Route 
                path="*" 
                element={
                  <WelcomeScreen 
                    onOpenProject={handleOpenProject}
                    appVersion={appState.appVersion}
                  />
                } 
              />
            </Routes>
          </motion.div>
        ) : (
          <motion.div
            key="ide"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <IDELayout 
              projectPath={appState.currentProject!}
              onCloseProject={handleCloseProject}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default App;
