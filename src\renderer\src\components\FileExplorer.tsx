import React, { useState, useEffect } from 'react';
import '../styles/FileExplorer.css';

interface FileExplorerProps {
  projectPath: string;
}

interface FileItem {
  name: string;
  path: string;
  isDirectory: boolean;
  isExpanded?: boolean;
  children?: FileItem[];
}

const FileExplorer: React.FC<FileExplorerProps> = ({ projectPath }) => {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const loadFiles = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Use the new file system API to read the directory
        const fileInfos = await window.electron.fileSystem.readDirectory(projectPath, false);

        const convertToFileItem = (fileInfo: EnhancedFileInfo): FileItem => ({
          name: fileInfo.name,
          path: fileInfo.path,
          isDirectory: fileInfo.isDirectory,
          isExpanded: false,
          children: fileInfo.isDirectory ? [] : undefined
        });

        const fileItems = fileInfos.map(convertToFileItem);
        setFiles(fileItems);
      } catch (err: any) {
        console.error('Failed to load files:', err);
        setError(`Failed to load project files: ${err.message || 'Unknown error'}`);
      } finally {
        setIsLoading(false);
      }
    };

    loadFiles();
  }, [projectPath]);
  
  const toggleDirectory = async (path: string) => {
    const updateFiles = async (items: FileItem[]): Promise<FileItem[]> => {
      const updatedItems: FileItem[] = [];

      for (const item of items) {
        if (item.path === path) {
          const newExpanded = !item.isExpanded;
          let children = item.children;

          // Load children if expanding and not already loaded
          if (newExpanded && item.isDirectory && (!children || children.length === 0)) {
            try {
              const fileInfos = await window.electron.fileSystem.readDirectory(item.path, false);
              children = fileInfos.map(fileInfo => ({
                name: fileInfo.name,
                path: fileInfo.path,
                isDirectory: fileInfo.isDirectory,
                isExpanded: false,
                children: fileInfo.isDirectory ? [] : undefined
              }));
            } catch (error) {
              console.error('Failed to load directory contents:', error);
              children = [];
            }
          }

          updatedItems.push({ ...item, isExpanded: newExpanded, children });
        } else if (item.children) {
          const updatedChildren = await updateFiles(item.children);
          updatedItems.push({ ...item, children: updatedChildren });
        } else {
          updatedItems.push(item);
        }
      }

      return updatedItems;
    };

    const updatedFiles = await updateFiles(files);
    setFiles(updatedFiles);
  };
  
  const handleFileClick = async (file: FileItem) => {
    if (file.isDirectory) {
      await toggleDirectory(file.path);
    } else {
      try {
        // Read file content
        const content = await window.electron.fileSystem.readFile(file.path);

        // Get file extension to determine language
        const extension = window.electron.path.extname(file.path).toLowerCase();
        let language = 'plaintext';

        // Map file extensions to Monaco editor language identifiers
        switch (extension) {
          case '.js':
            language = 'javascript';
            break;
          case '.jsx':
            language = 'javascript';
            break;
          case '.ts':
            language = 'typescript';
            break;
          case '.tsx':
            language = 'typescript';
            break;
          case '.html':
            language = 'html';
            break;
          case '.css':
            language = 'css';
            break;
          case '.json':
            language = 'json';
            break;
          case '.md':
            language = 'markdown';
            break;
          case '.py':
            language = 'python';
            break;
          case '.java':
            language = 'java';
            break;
          case '.c':
            language = 'c';
            break;
          case '.cpp':
            language = 'cpp';
            break;
          case '.cs':
            language = 'csharp';
            break;
          case '.go':
            language = 'go';
            break;
          case '.php':
            language = 'php';
            break;
          case '.rb':
            language = 'ruby';
            break;
          case '.rs':
            language = 'rust';
            break;
          case '.sh':
            language = 'shell';
            break;
          case '.sql':
            language = 'sql';
            break;
          case '.xml':
            language = 'xml';
            break;
          case '.yaml':
          case '.yml':
            language = 'yaml';
            break;
        }

        // Emit event to open file in editor
        window.electron.ipc.send('open-file', {
          path: file.path,
          name: file.name,
          content,
          language
        });
      } catch (error) {
        console.error('Failed to open file:', error);
      }
    }
  };
  
  const renderFileTree = (items: FileItem[], level = 0) => {
    return items.map(item => (
      <React.Fragment key={item.path}>
        <div 
          className={`file-item ${item.isDirectory ? 'directory' : 'file'}`}
          style={{ paddingLeft: `${level * 16}px` }}
          onClick={() => handleFileClick(item)}
        >
          <div className="file-icon">
            {item.isDirectory ? (
              item.isExpanded ? (
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M19 9l-7 7-7-7"/>
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M9 18l6-6-6-6"/>
                </svg>
              )
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                <polyline points="14 2 14 8 20 8"/>
              </svg>
            )}
          </div>
          <div className="file-name">{item.name}</div>
        </div>
        
        {item.isDirectory && item.isExpanded && item.children && (
          renderFileTree(item.children, level + 1)
        )}
      </React.Fragment>
    ));
  };
  
  return (
    <div className="file-explorer">
      <div className="explorer-header">
        <h3>Explorer</h3>
        <div className="explorer-actions">
          <button className="explorer-action" title="New File">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
              <polyline points="14 2 14 8 20 8"/>
              <line x1="12" y1="18" x2="12" y2="12"/>
              <line x1="9" y1="15" x2="15" y2="15"/>
            </svg>
          </button>
          <button className="explorer-action" title="New Folder">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"/>
              <line x1="12" y1="11" x2="12" y2="17"/>
              <line x1="9" y1="14" x2="15" y2="14"/>
            </svg>
          </button>
          <button className="explorer-action" title="Refresh">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M23 4v6h-6"/>
              <path d="M1 20v-6h6"/>
              <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"/>
            </svg>
          </button>
          <button className="explorer-action" title="Collapse All">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <line x1="18" y1="6" x2="6" y2="18"/>
              <line x1="6" y1="6" x2="18" y2="18"/>
            </svg>
          </button>
        </div>
      </div>
      
      <div className="explorer-content">
        {isLoading ? (
          <div className="explorer-loading">
            <div className="spinner-ring small"></div>
            <span>Loading files...</span>
          </div>
        ) : error ? (
          <div className="explorer-error">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <circle cx="12" cy="12" r="10"/>
              <line x1="12" y1="8" x2="12" y2="12"/>
              <line x1="12" y1="16" x2="12.01" y2="16"/>
            </svg>
            <span>{error}</span>
          </div>
        ) : (
          <div className="file-tree">
            {renderFileTree(files)}
          </div>
        )}
      </div>
    </div>
  );
};

export default FileExplorer;
