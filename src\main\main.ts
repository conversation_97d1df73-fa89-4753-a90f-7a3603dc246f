import { app, BrowserWindow, Menu, ipcMain, dialog, shell, MenuItemConstructorOptions } from 'electron';
import * as path from 'path';
import { autoUpdater } from 'electron-updater';
import { DatabaseManager } from './services/DatabaseManager';
import { FileSystemService } from './services/FileSystemService';
import { ProjectManager } from './services/ProjectManager';
import { TerminalService } from './services/TerminalService';

const isDev = process.env.NODE_ENV === 'development';

// Keep a global reference of the window object
let mainWindow: BrowserWindow | null = null;

// Initialize services
let dbManager: DatabaseManager;
let fileSystemService: FileSystemService;
let projectManager: ProjectManager;
let terminalService: TerminalService;

function createWindow(): void {
  // Create the browser window with Nusantara Glow styling
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1000,
    minHeight: 600,
    show: false, // Don't show until ready-to-show
    icon: path.join(__dirname, '../../assets/icons/kilatcode-icon.png'),
    titleBarStyle: 'hidden',
    titleBarOverlay: {
      color: '#0a0a0a',
      symbolColor: '#00d4ff',
      height: 30
    },
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: !isDev
    },
    backgroundColor: '#0a0a0a', // Nusantara Glow dark background
  });

  // Load the app
  if (isDev) {
    mainWindow.loadURL('http://localhost:3000');
    // Open DevTools in development
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, '../../dist/renderer/index.html'));
  }

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow?.show();
    
    // Focus on window
    if (isDev) {
      mainWindow?.focus();
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });
}

// Initialize services
function initializeServices(): void {
  // Initialize database manager
  dbManager = new DatabaseManager();

  // Initialize file system service
  fileSystemService = new FileSystemService();

  // Initialize project manager
  projectManager = new ProjectManager(dbManager, fileSystemService);

  // Initialize terminal service
  terminalService = new TerminalService();

  // Setup terminal event handlers
  setupTerminalEventHandlers();

  // Setup IPC handlers for services
  setupIpcHandlers();
}

// Setup terminal event handlers
function setupTerminalEventHandlers(): void {
  terminalService.on('terminal-data', (data) => {
    mainWindow?.webContents.send('terminal-data', data);
  });

  terminalService.on('terminal-exit', (data) => {
    mainWindow?.webContents.send('terminal-exit', data);
  });

  terminalService.on('terminal-error', (data) => {
    mainWindow?.webContents.send('terminal-error', data);
  });

  terminalService.on('terminal-created', (data) => {
    mainWindow?.webContents.send('terminal-created', data);
  });

  terminalService.on('terminal-killed', (data) => {
    mainWindow?.webContents.send('terminal-killed', data);
  });
}

// Setup IPC handlers for all services
function setupIpcHandlers(): void {
  // Database related handlers
  ipcMain.handle('db:get-recent-projects', async () => {
    return dbManager.getRecentProjects();
  });

  ipcMain.handle('db:get-settings', async (_, category: string) => {
    return dbManager.getSettingsByCategory(category);
  });

  ipcMain.handle('db:set-setting', async (_, key: string, value: string, category: string) => {
    return dbManager.setSetting(key, value, category);
  });

  // File system related handlers
  ipcMain.handle('fs:read-file', async (_, filePath: string) => {
    return fileSystemService.readFile(filePath);
  });

  ipcMain.handle('fs:write-file', async (_, filePath: string, content: string) => {
    return fileSystemService.writeFile(filePath, content);
  });

  ipcMain.handle('fs:read-directory', async (_, dirPath: string, recursive: boolean) => {
    return fileSystemService.readDirectory(dirPath, recursive);
  });

  ipcMain.handle('fs:create-file', async (_, filePath: string, content: string) => {
    return fileSystemService.createFile(filePath, content);
  });

  ipcMain.handle('fs:create-directory', async (_, dirPath: string) => {
    return fileSystemService.createDirectory(dirPath);
  });

  ipcMain.handle('fs:rename-file', async (_, oldPath: string, newPath: string) => {
    return fileSystemService.renameFile(oldPath, newPath);
  });

  ipcMain.handle('fs:delete-file', async (_, filePath: string) => {
    return fileSystemService.deleteFile(filePath);
  });

  ipcMain.handle('fs:delete-directory', async (_, dirPath: string, recursive: boolean) => {
    return fileSystemService.deleteDirectory(dirPath, recursive);
  });

  // Project related handlers
  ipcMain.handle('project:open', async (_, projectPath: string) => {
    return projectManager.openProject(projectPath);
  });

  ipcMain.handle('project:close', async () => {
    return projectManager.closeProject();
  });

  ipcMain.handle('project:create', async (_, projectPath: string, template: any) => {
    return projectManager.createProject(projectPath, template);
  });

  ipcMain.handle('project:get-current', () => {
    return projectManager.getCurrentProject();
  });

  ipcMain.handle('project:analyze', async (_, projectPath: string) => {
    return projectManager.analyzeProject(projectPath);
  });

  // Dialog handlers
  ipcMain.handle('dialog:openDirectory', async () => {
    if (!mainWindow) return { canceled: true };

    return dialog.showOpenDialog(mainWindow, {
      properties: ['openDirectory']
    });
  });

  ipcMain.handle('dialog:openFile', async (_, options: any) => {
    if (!mainWindow) return { canceled: true };

    return dialog.showOpenDialog(mainWindow, {
      properties: ['openFile'],
      filters: options?.filters || [{ name: 'All Files', extensions: ['*'] }]
    });
  });

  ipcMain.handle('dialog:saveFile', async (_, options: any) => {
    if (!mainWindow) return { canceled: true };

    return dialog.showSaveDialog(mainWindow, {
      filters: options?.filters || [{ name: 'All Files', extensions: ['*'] }]
    });
  });

  // Terminal handlers
  ipcMain.handle('terminal:create', async (_, options: any) => {
    return terminalService.createTerminal(options);
  });

  ipcMain.handle('terminal:write', async (_, id: number, data: string) => {
    return terminalService.write(id, data);
  });

  ipcMain.handle('terminal:resize', async (_, id: number, cols: number, rows: number) => {
    return terminalService.resize(id, cols, rows);
  });

  ipcMain.handle('terminal:kill', async (_, id: number) => {
    return terminalService.kill(id);
  });

  ipcMain.handle('terminal:get-all', async () => {
    return terminalService.getTerminals();
  });

  ipcMain.handle('terminal:get', async (_, id: number) => {
    return terminalService.getTerminal(id);
  });
}

// App event handlers
app.whenReady().then(() => {
  // Initialize services
  initializeServices();

  // Create main window
  createWindow();

  // Set application menu
  createApplicationMenu();

  // Check for updates (in production)
  if (!isDev) {
    autoUpdater.checkForUpdatesAndNotify();
  }
});

app.on('window-all-closed', () => {
  // On macOS, keep app running even when all windows are closed
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Cleanup resources before quitting
app.on('will-quit', () => {
  // Close database connection
  if (dbManager) {
    dbManager.close();
  }

  // Cleanup file system watchers
  if (fileSystemService) {
    fileSystemService.unwatchAll();
  }

  // Cleanup project manager
  if (projectManager) {
    projectManager.destroy();
  }

  // Cleanup terminal service
  if (terminalService) {
    terminalService.dispose();
  }
});

app.on('activate', () => {
  // On macOS, re-create window when dock icon is clicked
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Security: Prevent new window creation
app.on('web-contents-created', (_, contents) => {
  contents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });
});

// Create application menu
function createApplicationMenu(): void {
  const template: MenuItemConstructorOptions[] = [
    {
      label: 'File',
      submenu: [
        {
          label: 'New File',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow?.webContents.send('menu-new-file');
          }
        },
        {
          label: 'Open File',
          accelerator: 'CmdOrCtrl+O',
          click: async () => {
            if (!mainWindow) return;
            
            const result = await dialog.showOpenDialog(mainWindow, {
              properties: ['openFile'],
              filters: [
                { name: 'All Files', extensions: ['*'] }
              ]
            });
            
            if (!result.canceled) {
              mainWindow.webContents.send('menu-open-file', result.filePaths[0]);
            }
          }
        },
        {
          label: 'Open Folder',
          accelerator: 'CmdOrCtrl+Shift+O',
          click: async () => {
            if (!mainWindow) return;
            
            const result = await dialog.showOpenDialog(mainWindow, {
              properties: ['openDirectory']
            });
            
            if (!result.canceled && result.filePaths.length > 0) {
              mainWindow.webContents.send('menu-open-folder', result.filePaths[0]);
            }
          }
        },
        { type: 'separator' },
        {
          label: 'Save',
          accelerator: 'CmdOrCtrl+S',
          click: () => {
            mainWindow?.webContents.send('menu-save');
          }
        },
        {
          label: 'Save As',
          accelerator: 'CmdOrCtrl+Shift+S',
          click: () => {
            mainWindow?.webContents.send('menu-save-as');
          }
        },
        { type: 'separator' },
        {
          label: 'Exit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
        { role: 'selectAll' }
      ]
    },
    {
      label: 'View',
      submenu: [
        {
          label: 'Command Palette',
          accelerator: 'CmdOrCtrl+Shift+P',
          click: () => {
            mainWindow?.webContents.send('menu-command-palette');
          }
        },
        { type: 'separator' },
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Terminal',
      submenu: [
        {
          label: 'New Terminal',
          accelerator: 'CmdOrCtrl+Shift+`',
          click: () => {
            mainWindow?.webContents.send('menu-new-terminal');
          }
        }
      ]
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'About KilatCode IDE',
          click: () => {
            mainWindow?.webContents.send('menu-about');
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// IPC handlers will be added here as we build more features
ipcMain.handle('app-version', () => {
  return app.getVersion();
});

// Auto-updater events
autoUpdater.on('checking-for-update', () => {
  console.log('Checking for update...');
});

autoUpdater.on('update-available', (info) => {
  console.log('Update available.', info);
});

autoUpdater.on('update-not-available', (info) => {
  console.log('Update not available.', info);
});

autoUpdater.on('error', (err) => {
  console.log('Error in auto-updater.', err);
});

autoUpdater.on('download-progress', (progressObj) => {
  let log_message = `Download speed: ${progressObj.bytesPerSecond}`;
  log_message = `${log_message} - Downloaded ${progressObj.percent}%`;
  log_message = `${log_message} (${progressObj.transferred}/${progressObj.total})`;
  console.log(log_message);
});

autoUpdater.on('update-downloaded', (info) => {
  console.log('Update downloaded', info);
  autoUpdater.quitAndInstall();
});
