import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import '../styles/WelcomeScreen.css';

interface WelcomeScreenProps {
  onOpenProject: (projectPath: string) => void;
  appVersion: string;
}

interface RecentProject {
  path: string;
  name: string;
  lastOpened: string;
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ onOpenProject, appVersion }) => {
  const [recentProjects, setRecentProjects] = useState<RecentProject[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Load recent projects from database
    const loadRecentProjects = async () => {
      try {
        setIsLoading(true);
        const projects = await window.electron.database.getRecentProjects();

        const formattedProjects = projects.map(project => ({
          path: project.path,
          name: project.name,
          lastOpened: formatLastOpened(project.lastOpened)
        }));

        setRecentProjects(formattedProjects);
      } catch (error) {
        console.error('Failed to load recent projects:', error);
        // Fallback to empty array
        setRecentProjects([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadRecentProjects();
  }, []);

  const formatLastOpened = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffHours < 1) {
      return 'Just now';
    } else if (diffHours < 24) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return `${diffDays} days ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const handleOpenFolder = async () => {
    try {
      const result = await window.electron.dialog.openDirectory();
      if (result && !result.canceled && result.filePaths.length > 0) {
        // Open project using the new project API
        await window.electron.project.open(result.filePaths[0]);
        onOpenProject(result.filePaths[0]);
      }
    } catch (error) {
      console.error('Failed to open folder:', error);
    }
  };

  const handleCreateNewProject = () => {
    // This will be implemented when we add project templates
    console.log('Create new project clicked');
  };

  const handleOpenRecentProject = (projectPath: string) => {
    onOpenProject(projectPath);
  };

  return (
    <div className="welcome-screen batik-pattern">
      <div className="welcome-container">
        <header className="welcome-header">
          <motion.div 
            className="logo-container"
            initial={{ y: -20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="logo-icon">
              <div className="lightning-bolt"></div>
            </div>
            <h1 className="logo-text neon-text">KilatCode IDE</h1>
          </motion.div>
          
          <motion.p 
            className="welcome-subtitle"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            Modern Development Environment with Nusantara Glow
          </motion.p>
        </header>

        <motion.div 
          className="welcome-content"
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.5 }}
        >
          <div className="welcome-actions">
            <div className="action-card" onClick={handleCreateNewProject}>
              <div className="action-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M12 5v14M5 12h14"/>
                </svg>
              </div>
              <div className="action-text">
                <h3>New Project</h3>
                <p>Create a new project from template</p>
              </div>
            </div>
            
            <div className="action-card" onClick={handleOpenFolder}>
              <div className="action-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"/>
                </svg>
              </div>
              <div className="action-text">
                <h3>Open Folder</h3>
                <p>Open an existing project folder</p>
              </div>
            </div>
            
            <div className="action-card">
              <div className="action-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
                </svg>
              </div>
              <div className="action-text">
                <h3>Clone Repository</h3>
                <p>Clone a Git repository</p>
              </div>
            </div>
          </div>

          <div className="recent-projects">
            <h2>Recent Projects</h2>
            
            {isLoading ? (
              <div className="loading-projects">
                <div className="spinner-ring small"></div>
                <span>Loading recent projects...</span>
              </div>
            ) : recentProjects.length > 0 ? (
              <div className="projects-list">
                {recentProjects.map((project, index) => (
                  <div 
                    key={index} 
                    className="project-item"
                    onClick={() => handleOpenRecentProject(project.path)}
                  >
                    <div className="project-icon">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"/>
                      </svg>
                    </div>
                    <div className="project-info">
                      <h3>{project.name}</h3>
                      <p>{project.path}</p>
                      <span className="project-time">{project.lastOpened}</span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-projects">
                <p>No recent projects found</p>
              </div>
            )}
          </div>
        </motion.div>

        <footer className="welcome-footer">
          <div className="footer-links">
            <a href="#" className="footer-link">Documentation</a>
            <a href="#" className="footer-link">Marketplace</a>
            <a href="#" className="footer-link">Report Issue</a>
          </div>
          <div className="version-info">
            <span>KilatCode IDE v{appVersion}</span>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default WelcomeScreen;
