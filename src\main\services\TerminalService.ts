import { EventEmitter } from 'events';
import { spawn, ChildProcess } from 'child_process';
import * as os from 'os';
import * as path from 'path';

export interface TerminalOptions {
  shell?: string;
  cwd?: string;
  env?: NodeJS.ProcessEnv;
  cols?: number;
  rows?: number;
}

export interface TerminalProcess {
  id: number;
  process: ChildProcess;
  shell: string;
  cwd: string;
  cols: number;
  rows: number;
  created: Date;
}

export class TerminalService extends EventEmitter {
  private terminals: Map<number, TerminalProcess> = new Map();
  private nextTerminalId: number = 1;

  constructor() {
    super();
  }

  /**
   * Create a new terminal process
   */
  public createTerminal(options: TerminalOptions = {}): TerminalProcess {
    const shell = options.shell || this.getDefaultShell();
    const cwd = options.cwd || os.homedir();
    const env = options.env || { ...process.env };
    const cols = options.cols || 80;
    const rows = options.rows || 24;
    
    // Spawn the process
    const shellArgs = this.getShellArgs(shell);
    const childProcess = spawn(shell, shellArgs, {
      cwd,
      env,
      stdio: ['pipe', 'pipe', 'pipe'],
      windowsHide: true
    });
    
    const terminalId = this.nextTerminalId++;
    
    const terminal: TerminalProcess = {
      id: terminalId,
      process: childProcess,
      shell,
      cwd,
      cols,
      rows,
      created: new Date()
    };
    
    // Setup event handlers
    childProcess.stdout?.on('data', (data) => {
      this.emit('terminal-data', { id: terminalId, data: data.toString() });
    });
    
    childProcess.stderr?.on('data', (data) => {
      this.emit('terminal-data', { id: terminalId, data: data.toString() });
    });
    
    childProcess.on('exit', (code) => {
      this.emit('terminal-exit', { id: terminalId, code });
      this.terminals.delete(terminalId);
    });
    
    childProcess.on('error', (error) => {
      this.emit('terminal-error', { id: terminalId, error: error.message });
    });
    
    // Store the terminal
    this.terminals.set(terminalId, terminal);
    
    // Emit created event
    this.emit('terminal-created', { id: terminalId, shell, cwd });
    
    return terminal;
  }

  /**
   * Write data to a terminal
   */
  public write(id: number, data: string): boolean {
    const terminal = this.terminals.get(id);
    
    if (!terminal) {
      return false;
    }
    
    try {
      terminal.process.stdin?.write(data);
      return true;
    } catch (error) {
      console.error(`Failed to write to terminal ${id}:`, error);
      return false;
    }
  }

  /**
   * Resize a terminal
   */
  public resize(id: number, cols: number, rows: number): boolean {
    const terminal = this.terminals.get(id);
    
    if (!terminal) {
      return false;
    }
    
    try {
      // Update terminal dimensions
      terminal.cols = cols;
      terminal.rows = rows;
      
      // Emit resize event
      this.emit('terminal-resize', { id, cols, rows });
      
      return true;
    } catch (error) {
      console.error(`Failed to resize terminal ${id}:`, error);
      return false;
    }
  }

  /**
   * Kill a terminal process
   */
  public kill(id: number): boolean {
    const terminal = this.terminals.get(id);
    
    if (!terminal) {
      return false;
    }
    
    try {
      terminal.process.kill();
      this.terminals.delete(id);
      this.emit('terminal-killed', { id });
      return true;
    } catch (error) {
      console.error(`Failed to kill terminal ${id}:`, error);
      return false;
    }
  }

  /**
   * Get all active terminals
   */
  public getTerminals(): TerminalProcess[] {
    return Array.from(this.terminals.values()).map(terminal => ({
      ...terminal,
      process: undefined as any // Don't send the process object to renderer
    }));
  }

  /**
   * Get a terminal by ID
   */
  public getTerminal(id: number): TerminalProcess | undefined {
    const terminal = this.terminals.get(id);
    
    if (!terminal) {
      return undefined;
    }
    
    return {
      ...terminal,
      process: undefined as any // Don't send the process object to renderer
    };
  }

  /**
   * Get the default shell based on the platform
   */
  private getDefaultShell(): string {
    if (process.platform === 'win32') {
      return process.env.COMSPEC || 'cmd.exe';
    }
    
    return process.env.SHELL || '/bin/bash';
  }

  /**
   * Get shell arguments based on the shell type
   */
  private getShellArgs(shell: string): string[] {
    const shellName = path.basename(shell).toLowerCase();
    
    if (process.platform === 'win32') {
      if (shellName === 'powershell.exe' || shellName === 'pwsh.exe') {
        return ['-NoLogo', '-NoExit'];
      }
      
      if (shellName === 'cmd.exe') {
        return [];
      }
      
      // Git Bash or other shells
      return ['--login'];
    }
    
    // Unix shells
    if (shellName === 'bash') {
      return ['--login'];
    }
    
    if (shellName === 'zsh') {
      return ['--login'];
    }
    
    return [];
  }

  /**
   * Clean up all terminals
   */
  public dispose(): void {
    for (const [id, terminal] of this.terminals) {
      try {
        terminal.process.kill();
      } catch (error) {
        console.error(`Failed to kill terminal ${id} during cleanup:`, error);
      }
    }
    
    this.terminals.clear();
    this.removeAllListeners();
  }
}
