/* Terminal Component Styles */

.terminal-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--primary-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.terminal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--secondary-bg);
  border-bottom: 1px solid var(--border-primary);
  min-height: 32px;
}

.terminal-title {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
  user-select: none;
}

.terminal-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.terminal-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: none;
  border-radius: var(--radius-sm);
  background: transparent;
  color: var(--text-muted);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.terminal-action:hover {
  background: var(--tertiary-bg);
  color: var(--text-primary);
}

.terminal-action:active {
  transform: scale(0.95);
}

.terminal {
  flex: 1;
  padding: var(--spacing-sm);
  background: var(--primary-bg);
  overflow: hidden;
}

/* XTerm.js overrides for Nusantara Glow theme */
.terminal .xterm {
  font-family: 'Fira Code', 'Cascadia Code', Consolas, 'Courier New', monospace !important;
  font-size: 14px !important;
  line-height: 1.2 !important;
}

.terminal .xterm-viewport {
  background: var(--primary-bg) !important;
}

.terminal .xterm-screen {
  background: var(--primary-bg) !important;
}

.terminal .xterm-cursor-layer {
  z-index: 2;
}

.terminal .xterm-cursor-bar {
  background: var(--neon-cyan) !important;
  box-shadow: 0 0 5px var(--neon-cyan);
}

.terminal .xterm-cursor-block {
  background: var(--neon-cyan) !important;
  color: var(--primary-bg) !important;
  box-shadow: 0 0 5px var(--neon-cyan);
}

.terminal .xterm-cursor-underline {
  border-bottom: 2px solid var(--neon-cyan) !important;
  box-shadow: 0 0 5px var(--neon-cyan);
}

.terminal .xterm-selection {
  background: rgba(0, 212, 255, 0.3) !important;
}

/* Terminal scrollbar */
.terminal .xterm-viewport::-webkit-scrollbar {
  width: 8px;
}

.terminal .xterm-viewport::-webkit-scrollbar-track {
  background: var(--secondary-bg);
}

.terminal .xterm-viewport::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: var(--radius-sm);
}

.terminal .xterm-viewport::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Terminal panel styles */
.terminal-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--secondary-bg);
  border-top: 1px solid var(--border-primary);
}

.terminal-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--tertiary-bg);
  border-bottom: 1px solid var(--border-primary);
  min-height: 36px;
}

.terminal-panel-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  user-select: none;
}

.terminal-panel-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.terminal-panel-action {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xs);
  border: none;
  border-radius: var(--radius-sm);
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 12px;
}

.terminal-panel-action:hover {
  background: var(--accent-bg);
  color: var(--text-primary);
}

.terminal-panel-action.active {
  background: var(--neon-cyan);
  color: var(--primary-bg);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

.terminal-tabs {
  display: flex;
  align-items: center;
  background: var(--secondary-bg);
  border-bottom: 1px solid var(--border-primary);
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.terminal-tabs::-webkit-scrollbar {
  display: none;
}

.terminal-tab {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  background: transparent;
  border: none;
  border-right: 1px solid var(--border-primary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
  min-width: 120px;
  max-width: 200px;
}

.terminal-tab:hover {
  background: var(--tertiary-bg);
  color: var(--text-primary);
}

.terminal-tab.active {
  background: var(--primary-bg);
  color: var(--text-accent);
  border-bottom: 2px solid var(--neon-cyan);
}

.terminal-tab-title {
  flex: 1;
  text-align: left;
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.terminal-tab-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border: none;
  border-radius: var(--radius-sm);
  background: transparent;
  color: var(--text-muted);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.terminal-tab-close:hover {
  background: var(--error);
  color: var(--text-primary);
}

.terminal-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Terminal status indicators */
.terminal-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 10px;
  color: var(--text-muted);
}

.terminal-status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--text-muted);
}

.terminal-status-dot.running {
  background: var(--success);
  box-shadow: 0 0 4px var(--success);
}

.terminal-status-dot.error {
  background: var(--error);
  box-shadow: 0 0 4px var(--error);
}

.terminal-status-dot.exited {
  background: var(--warning);
  box-shadow: 0 0 4px var(--warning);
}

/* Responsive design */
@media (max-width: 768px) {
  .terminal-header {
    padding: var(--spacing-xs) var(--spacing-sm);
  }
  
  .terminal-title {
    font-size: 11px;
  }
  
  .terminal .xterm {
    font-size: 12px !important;
  }
  
  .terminal-tab {
    min-width: 100px;
    max-width: 150px;
    padding: var(--spacing-xs) var(--spacing-sm);
  }
}
