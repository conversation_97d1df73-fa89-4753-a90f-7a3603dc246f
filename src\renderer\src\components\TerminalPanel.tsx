import React, { useState, useEffect } from 'react';
import Terminal from './Terminal';
import '../styles/Terminal.css';

interface TerminalTab {
  id: number;
  title: string;
  shell: string;
  cwd: string;
  status: 'running' | 'exited' | 'error';
}

interface TerminalPanelProps {
  projectPath?: string;
  isVisible: boolean;
  onToggle: () => void;
}

const TerminalPanel: React.FC<TerminalPanelProps> = ({
  projectPath,
  isVisible,
  onToggle
}) => {
  const [terminals, setTerminals] = useState<TerminalTab[]>([]);
  const [activeTerminalId, setActiveTerminalId] = useState<number | null>(null);

  // Create initial terminal when panel becomes visible
  useEffect(() => {
    if (isVisible && terminals.length === 0) {
      createNewTerminal();
    }
  }, [isVisible]);

  const createNewTerminal = async () => {
    try {
      // Determine shell and cwd
      const shell = getDefaultShell();
      const cwd = projectPath || await getHomeDirectory();

      // Create terminal process
      const termProcess = await window.electron.terminal.create({
        shell,
        cwd,
        cols: 80,
        rows: 24
      });

      // Create terminal tab
      const newTab: TerminalTab = {
        id: termProcess.id,
        title: `Terminal ${termProcess.id}`,
        shell: termProcess.shell,
        cwd: termProcess.cwd,
        status: 'running'
      };

      setTerminals(prev => [...prev, newTab]);
      setActiveTerminalId(termProcess.id);
    } catch (error) {
      console.error('Failed to create terminal:', error);
    }
  };

  const closeTerminal = (terminalId: number) => {
    // Kill the terminal process
    window.electron.terminal.kill(terminalId).catch(console.error);

    // Remove from tabs
    setTerminals(prev => {
      const newTerminals = prev.filter(t => t.id !== terminalId);
      
      // If closing active terminal, switch to another one
      if (activeTerminalId === terminalId) {
        if (newTerminals.length > 0) {
          setActiveTerminalId(newTerminals[0].id);
        } else {
          setActiveTerminalId(null);
        }
      }
      
      return newTerminals;
    });
  };

  const getDefaultShell = (): string => {
    const platform = window.electron.appInfo.getPlatform();
    
    if (platform === 'win32') {
      return 'cmd.exe';
    }
    
    return '/bin/bash';
  };

  const getHomeDirectory = async (): Promise<string> => {
    const platform = window.electron.appInfo.getPlatform();
    
    if (platform === 'win32') {
      return process.env.USERPROFILE || 'C:\\Users\\<USER>