{"name": "kilatcode-ide", "version": "1.0.0", "description": "KilatCode IDE - Modern Desktop IDE with Nusantara Glow Theme", "main": "dist/main/main.js", "homepage": "./", "author": {"name": "KilatCode Team", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["ide", "editor", "electron", "react", "monaco", "nusantara", "indonesia"], "scripts": {"dev": "concurrently \"npm run dev:main\" \"npm run dev:renderer\"", "dev:main": "nodemon --exec electron dist/main/main.js --watch src/main --ext js,ts", "dev:renderer": "vite", "build": "npm run build:main && npm run build:renderer", "build:main": "tsc -p src/main/tsconfig.json", "build:renderer": "vite build", "dist": "npm run build && electron-builder", "dist:win": "npm run build && electron-builder --win", "dist:mac": "npm run build && electron-builder --mac", "dist:linux": "npm run build && electron-builder --linux", "start": "npm run build:main && cross-env NODE_ENV=development electron .", "electron": "cross-env NODE_ENV=development electron .", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix"}, "dependencies": {"@monaco-editor/react": "^4.6.0", "axios": "^1.6.2", "better-sqlite3": "^9.4.3", "chokidar": "^3.5.3", "classnames": "^2.3.2", "electron-store": "^8.1.0", "electron-updater": "^6.1.7", "express": "^4.18.2", "framer-motion": "^10.16.16", "lodash": "^4.17.21", "lucide-react": "^0.294.0", "monaco-editor": "^0.45.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-split-pane": "^0.1.92", "react-virtualized": "^9.22.5", "uuid": "^9.0.1", "ws": "^8.14.2", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-web-links": "^0.9.0", "zustand": "^4.5.0"}, "devDependencies": {"@types/better-sqlite3": "^7.6.9", "@types/node": "^20.10.4", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@types/uuid": "^9.0.7", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "@vitejs/plugin-react": "^4.2.0", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^27.1.3", "electron-builder": "^24.8.1", "eslint": "^8.54.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "typescript": "^5.3.2", "vite": "^5.0.6"}, "build": {"appId": "com.kilatcode.ide", "productName": "KilatCode IDE", "directories": {"output": "release"}, "files": ["dist/**/*", "node_modules/**/*", "package.json"], "mac": {"category": "public.app-category.developer-tools", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "msi", "arch": ["x64", "ia32"]}]}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}, {"target": "rpm", "arch": ["x64"]}]}, "publish": {"provider": "github", "owner": "kilatcode", "repo": "kilatcode-ide"}}}